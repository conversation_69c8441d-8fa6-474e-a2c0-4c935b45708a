#!/usr/bin/env python3
"""
Debug script to isolate the Azure import issue
"""

import sys
import os
import traceback

print("=== DEBUGGING IMPORT ISSUE ===")

# Set service type
os.environ['SERVICE_TYPE'] = 'aws'
print(f"SERVICE_TYPE set to: {os.environ.get('SERVICE_TYPE')}")

# Add shared path
sys.path.append('/app/shared')
print("Added /app/shared to sys.path")

# Test individual imports
print("\n1. Testing job_manager import...")
try:
    from job_manager import job_manager
    print("✓ job_manager imported successfully")
except Exception as e:
    print(f"✗ job_manager import failed: {e}")
    traceback.print_exc()

print("\n2. Testing steampipe_health import...")
try:
    import steampipe_health
    print("✓ steampipe_health module imported successfully")
except Exception as e:
    print(f"✗ steampipe_health import failed: {e}")
    traceback.print_exc()

print("\n3. Testing specific function import...")
try:
    from steampipe_health import initialize_steampipe_on_startup
    print("✓ initialize_steampipe_on_startup imported successfully")
except Exception as e:
    print(f"✗ initialize_steampipe_on_startup import failed: {e}")
    traceback.print_exc()

print("\n4. Testing ensure_steampipe_running import...")
try:
    from steampipe_health import ensure_steampipe_running
    print("✓ ensure_steampipe_running imported successfully")
except Exception as e:
    print(f"✗ ensure_steampipe_running import failed: {e}")
    traceback.print_exc()

print("\n5. Checking for azure module in sys.modules...")
azure_modules = [name for name in sys.modules.keys() if 'azure' in name.lower()]
if azure_modules:
    print(f"Found Azure modules in sys.modules: {azure_modules}")
else:
    print("No Azure modules found in sys.modules")

print("\n6. Checking Python path...")
for path in sys.path:
    print(f"  {path}")

print("\n=== DEBUG COMPLETE ===")
