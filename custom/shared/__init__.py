"""
Shared authentication module for cloud providers.
"""

# Import only what's needed to avoid loading unnecessary dependencies
try:
    from .auth_factory import (
        CloudAuthFactory,
        get_service_credentials
    )
except ImportError as e:
    # If auth_factory fails to import, provide fallback
    CloudAuthFactory = None
    get_service_credentials = None

try:
    from .auth_base import (
        <PERSON><PERSON><PERSON><PERSON><PERSON>,
        SecretManagerHelper,
        create_auth_blueprint,
        logger,
        BASE_DIR,
        AUTHENTICATION_FOLDER
    )
except ImportError as e:
    # Provide fallbacks
    CloudAuthBase = None
    SecretManagerHelper = None
    create_auth_blueprint = None
    logger = None
    BASE_DIR = None
    AUTHENTICATION_FOLDER = None

# Don't import auth blueprint by default to avoid dependency issues
auth = None

__all__ = [
    'CloudAuthFactory',
    'get_service_credentials',
    'auth',
    'CloudAuthBase',
    'SecretManagerHelper',
    'create_auth_blueprint',
    'logger',
    'BASE_DIR',
    'AUTHENTICATION_FOLDER'
]