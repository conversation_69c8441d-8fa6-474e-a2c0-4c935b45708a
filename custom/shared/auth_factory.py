"""
Authentication factory module that provides a unified interface for all cloud providers.
"""

from typing import <PERSON><PERSON>, Optional, Any
from .auth_base import create_auth_blueprint, logger


class CloudAuthFactory:
    """Factory class for creating cloud-specific authentication instances."""

    @classmethod
    def _get_auth_class(cls, cloud_provider: str):
        """Lazy import of auth classes to avoid importing unnecessary dependencies."""
        cloud_provider = cloud_provider.lower()

        if cloud_provider == 'aws':
            from .auth_aws import AWSAuth
            return AWSAuth
        elif cloud_provider == 'azure':
            from .auth_azure import AzureAuth
            return AzureAuth
        elif cloud_provider == 'gcp':
            from .auth_gcp import GCPAuth
            return GCPAuth
        else:
            raise ValueError(f"Unsupported cloud provider: {cloud_provider}. "
                           f"Supported providers: aws, azure, gcp")

    @classmethod
    def create_auth(cls, cloud_provider: str):
        """
        Create an authentication instance for the specified cloud provider.

        Args:
            cloud_provider: Cloud provider name (aws, azure, gcp)

        Returns:
            Authentication instance for the specified cloud provider

        Raises:
            ValueError: If cloud provider is not supported
        """
        auth_class = cls._get_auth_class(cloud_provider)
        return auth_class()
    
    @classmethod
    def get_service_credentials(cls, cloud_provider: str) -> Tuple[Optional[Any], Optional[str]]:
        """
        Get service credentials for the specified cloud provider.
        
        Args:
            cloud_provider: Cloud provider name (aws, azure, gcp)
            
        Returns:
            Tuple of (credentials, account/project identifier)
        """
        try:
            auth_instance = cls.create_auth(cloud_provider)
            return auth_instance.get_service_credentials()
        except Exception as e:
            logger.error(f"Failed to get credentials for {cloud_provider}: {str(e)}")
            return None, str(e)


# Convenience function for backward compatibility
def get_service_credentials(cloud_provider: str) -> Tuple[Optional[Any], Optional[str]]:
    """
    Get service credentials for the specified cloud provider.
    
    This is a convenience function that maintains backward compatibility
    with existing code that imports get_service_credentials directly.
    
    Args:
        cloud_provider: Cloud provider name (aws, azure, gcp)
        
    Returns:
        Tuple of (credentials, account/project identifier)
    """
    return CloudAuthFactory.get_service_credentials(cloud_provider)


# Lazy creation of auth blueprint to avoid import issues
auth = None

def get_auth_blueprint():
    """Get the auth blueprint, creating it lazily if needed."""
    global auth
    if auth is None:
        auth = create_auth_blueprint('auth')
    return auth