import os
import json
import logging
from typing import Dict, Optional, Tuple
from google.cloud import secretmanager
from google.api_core import exceptions

logger = logging.getLogger(__name__)

class SecretManagerClient:
    """Client for managing customer credentials in Google Secret Manager"""
    
    def __init__(self, project_id: str = None, client_name: str = None):
        self.project_id = project_id or os.getenv('GCP_PROJECT_ID')
        self.client_name = client_name or os.getenv('CLIENT_NAME', 'ironfort')
        self.client = secretmanager.SecretManagerServiceClient()
        
    def _get_secret_name(self, customer: str, cloud: str) -> str:
        """Generate secret name using naming convention: {client}-{customer}-{cloud}"""
        return f"{self.client_name}-{customer}-{cloud}"
    
    def _get_secret_path(self, secret_name: str, version: str = "latest") -> str:
        """Get the full resource path for a secret"""
        return f"projects/{self.project_id}/secrets/{secret_name}/versions/{version}"
    
    def get_customer_credentials(self, customer: str, cloud: str) -> <PERSON><PERSON>[Optional[Dict], Optional[str]]:
        """
        Retrieve customer credentials from Secret Manager
        
        Args:
            customer: Customer identifier
            cloud: Cloud provider (aws, azure, gcp)
            
        Returns:
            Tuple of (credentials_dict, error_message)
        """
        try:
            secret_name = self._get_secret_name(customer, cloud)
            secret_path = self._get_secret_path(secret_name)
            
            logger.info(f"Retrieving secret: {secret_name}")
            
            # Access the secret
            response = self.client.access_secret_version(request={"name": secret_path})
            
            # Decode the secret payload
            secret_value = response.payload.data.decode("UTF-8")
            
            # Parse JSON credentials
            credentials = json.loads(secret_value)
            
            logger.info(f"Successfully retrieved credentials for {customer}/{cloud}")
            return credentials, None
            
        except exceptions.NotFound:
            error_msg = f"No credentials found for customer '{customer}' and cloud '{cloud}'"
            logger.warning(error_msg)
            return None, error_msg
            
        except json.JSONDecodeError as e:
            error_msg = f"Invalid JSON in secret for {customer}/{cloud}: {str(e)}"
            logger.error(error_msg)
            return None, error_msg
            
        except Exception as e:
            error_msg = f"Error retrieving credentials: {str(e)}"
            logger.error(error_msg)
            return None, error_msg
    
    def create_customer_credentials(self, customer: str, cloud: str, credentials: Dict) -> Tuple[bool, Optional[str]]:
        """
        Create customer credentials in Secret Manager
        
        Args:
            customer: Customer identifier
            cloud: Cloud provider (aws, azure, gcp)
            credentials: Dictionary of credentials to store
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            secret_name = self._get_secret_name(customer, cloud)
            parent = f"projects/{self.project_id}"
            
            # Create the secret
            try:
                secret = self.client.create_secret(
                    request={
                        "parent": parent,
                        "secret_id": secret_name,
                        "secret": {
                            "replication": {
                                "automatic": {}
                            }
                        }
                    }
                )
                logger.info(f"Created secret: {secret_name}")
            except exceptions.AlreadyExists:
                logger.info(f"Secret {secret_name} already exists")
            
            # Add secret version with credentials
            secret_path = f"{parent}/secrets/{secret_name}"
            payload = json.dumps(credentials).encode("UTF-8")
            
            response = self.client.add_secret_version(
                request={
                    "parent": secret_path,
                    "payload": {"data": payload}
                }
            )
            
            logger.info(f"Added credentials version for {customer}/{cloud}")
            return True, None
            
        except Exception as e:
            error_msg = f"Error creating credentials: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def update_customer_credentials(self, customer: str, cloud: str, credentials: Dict) -> Tuple[bool, Optional[str]]:
        """
        Update customer credentials (adds a new version)
        
        Args:
            customer: Customer identifier
            cloud: Cloud provider (aws, azure, gcp)
            credentials: Dictionary of credentials to store
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            secret_name = self._get_secret_name(customer, cloud)
            parent = f"projects/{self.project_id}/secrets/{secret_name}"
            
            # Add new secret version
            payload = json.dumps(credentials).encode("UTF-8")
            
            response = self.client.add_secret_version(
                request={
                    "parent": parent,
                    "payload": {"data": payload}
                }
            )
            
            logger.info(f"Updated credentials for {customer}/{cloud}")
            return True, None
            
        except exceptions.NotFound:
            error_msg = f"Secret not found for {customer}/{cloud}. Create it first."
            logger.error(error_msg)
            return False, error_msg
            
        except Exception as e:
            error_msg = f"Error updating credentials: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def delete_customer_credentials(self, customer: str, cloud: str) -> Tuple[bool, Optional[str]]:
        """
        Delete customer credentials from Secret Manager
        
        Args:
            customer: Customer identifier
            cloud: Cloud provider (aws, azure, gcp)
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            secret_name = self._get_secret_name(customer, cloud)
            secret_path = f"projects/{self.project_id}/secrets/{secret_name}"
            
            # Delete the secret
            self.client.delete_secret(request={"name": secret_path})
            
            logger.info(f"Deleted credentials for {customer}/{cloud}")
            return True, None
            
        except exceptions.NotFound:
            error_msg = f"No credentials found for {customer}/{cloud}"
            logger.warning(error_msg)
            return False, error_msg
            
        except Exception as e:
            error_msg = f"Error deleting credentials: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def list_customer_credentials(self, customer: str) -> Tuple[Optional[list], Optional[str]]:
        """
        List all credentials for a customer
        
        Args:
            customer: Customer identifier
            
        Returns:
            Tuple of (list of cloud providers, error_message)
        """
        try:
            parent = f"projects/{self.project_id}"
            prefix = f"{self.client_name}-{customer}-"
            
            # List secrets with filter
            secrets = self.client.list_secrets(
                request={
                    "parent": parent,
                    "filter": f'name:"{prefix}"'
                }
            )
            
            cloud_providers = []
            for secret in secrets:
                # Extract cloud provider from secret name
                secret_name = secret.name.split('/')[-1]
                if secret_name.startswith(prefix):
                    cloud = secret_name.replace(prefix, '')
                    cloud_providers.append(cloud)
            
            logger.info(f"Found credentials for {customer}: {cloud_providers}")
            return cloud_providers, None
            
        except Exception as e:
            error_msg = f"Error listing credentials: {str(e)}"
            logger.error(error_msg)
            return None, error_msg


# Expected credential formats for each cloud provider
CREDENTIAL_SCHEMAS = {
    "aws": {
        "required": ["access_key_id", "secret_access_key"],
        "optional": ["session_token", "region"]
    },
    "azure": {
        "required": ["tenant_id", "client_id", "client_secret"],
        "optional": ["subscription_id"]
    },
    "gcp": **********************************************************************************************************************************************************************************************************************
}

def validate_credentials(cloud: str, credentials: Dict) -> Tuple[bool, Optional[str]]:
    """
    Validate credentials against expected schema
    
    Args:
        cloud: Cloud provider (aws, azure, gcp)
        credentials: Dictionary of credentials
        
    Returns:
        Tuple of (valid, error_message)
    """
    if cloud not in CREDENTIAL_SCHEMAS:
        return False, f"Unknown cloud provider: {cloud}"
    
    schema = CREDENTIAL_SCHEMAS[cloud]
    
    # Check required fields
    missing_fields = []
    for field in schema["required"]:
        if field not in credentials or not credentials[field]:
            missing_fields.append(field)
    
    if missing_fields:
        return False, f"Missing required fields: {', '.join(missing_fields)}"
    
    return True, None