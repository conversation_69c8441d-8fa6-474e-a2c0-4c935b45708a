"""
Steampipe Health Check and Recovery Module
Ensures Steampipe service is running before benchmark execution
"""

import subprocess
import time
import logging
import os
import threading
import re
import tempfile
import shutil

logger = logging.getLogger(__name__)

# Global lock to prevent concurrent service operations
_service_lock = threading.Lock()
_service_initialized = False

def kill_steampipe_processes():
    """Kill all Steampipe processes more aggressively"""
    try:
        # First try to stop the service normally
        try:
            subprocess.run(
                ["steampipe", "service", "stop", "--force"],
                capture_output=True,
                text=True,
                timeout=30,
                check=False
            )
            time.sleep(1)
        except:
            pass
        
        # Get PIDs of steampipe processes
        try:
            ps_result = subprocess.run(
                ["ps", "aux"],
                capture_output=True,
                text=True,
                check=False
            )
            
            for line in ps_result.stdout.splitlines():
                if "steampipe" in line and "ps aux" not in line:
                    parts = line.split()
                    if len(parts) > 1:
                        pid = parts[1]
                        try:
                            subprocess.run(["kill", "-9", pid], check=False)
                            logger.info(f"Killed Steampipe process with PID: {pid}")
                        except:
                            pass
        except:
            pass
        
        # Also try pkill as backup
        subprocess.run(["pkill", "-9", "-f", "steampipe"], check=False, capture_output=True)
        subprocess.run(["pkill", "-9", "-f", "postgres.*steampipe"], check=False, capture_output=True)
        
        time.sleep(2)
        logger.info("Completed Steampipe process cleanup")
    except Exception as e:
        logger.error(f"Error killing Steampipe processes: {e}")

def check_steampipe_health():
    """Check if Steampipe service is healthy with cloud-optimized checks"""
    try:
        # First check service status
        status_result = subprocess.run(
            ["steampipe", "service", "status"],
            capture_output=True,
            text=True,
            timeout=15
        )

        # If service status shows it's not running, return false immediately
        if status_result.returncode != 0:
            logger.debug(f"Service status check failed: {status_result.stderr}")
            return False

        # Try a simple query with longer timeout for cloud environments
        result = subprocess.run(
            ["steampipe", "query", "select 1 as test", "--output=json"],
            capture_output=True,
            text=True,
            timeout=45  # Increased timeout for cloud
        )

        if result.returncode == 0:
            logger.debug("Steampipe health check passed")
            return True
        else:
            logger.debug(f"Steampipe query failed: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        logger.error("Steampipe health check timed out")
        return False
    except Exception as e:
        logger.error(f"Steampipe health check failed: {e}")
        return False

def check_and_install_required_plugins():
    """Check if required plugins are installed based on service type"""
    try:
        # Determine service type from environment or working directory
        service_type = os.getenv('SERVICE_TYPE', 'unknown')
        if service_type == 'unknown':
            # Try to detect from current working directory
            cwd = os.getcwd()
            if 'gcp' in cwd:
                service_type = 'gcp'
            elif 'aws' in cwd:
                service_type = 'aws'
            elif 'azure' in cwd:
                service_type = 'azure'

        logger.info(f"Detected service type: {service_type}")

        # Check current plugins
        logger.info("Checking installed Steampipe plugins...")
        list_result = subprocess.run(
            ["steampipe", "plugin", "list"],
            capture_output=True,
            text=True,
            timeout=60
        )

        if list_result.returncode == 0:
            logger.info(f"Installed plugins:\n{list_result.stdout}")

            # Define required plugins per service
            required_plugins = {
                'gcp': ['gcp'],
                'aws': ['aws'],
                'azure': ['azure'],
                'unknown': []  # Don't install anything if we can't detect
            }

            plugins_to_check = required_plugins.get(service_type, [])

            for plugin in plugins_to_check:
                if plugin not in list_result.stdout:
                    logger.warning(f"{plugin} plugin not found in installed plugins")

                    # Try to install the missing plugin
                    logger.info(f"Attempting to install {plugin} plugin from registry...")
                    install_result = subprocess.run(
                        ["steampipe", "plugin", "install", plugin],
                        capture_output=True,
                        text=True,
                        timeout=60
                    )

                    if install_result.returncode == 0:
                        logger.info(f"{plugin} plugin installed successfully")
                    else:
                        logger.error(f"Failed to install {plugin} plugin: {install_result.stderr}")
                        # Don't fail completely, just log the error
                else:
                    logger.info(f"{plugin} plugin is already installed")

            return True
        else:
            logger.error(f"Failed to list plugins: {list_result.stderr}")
            return False

    except Exception as e:
        logger.error(f"Error checking/installing plugins: {e}")
        return False

def clean_steampipe_state():
    """Clean up Steampipe state files"""
    try:
        # Remove various state files that might cause issues
        state_dirs = [
            os.path.expanduser("~/.steampipe/internal"),
            os.path.expanduser("~/.steampipe/db/14.2.0/data"),
            os.path.expanduser("~/.steampipe/db/14.2.0/postgres.pid"),
            "/tmp/.s.PGSQL.9193",
            "/tmp/.s.PGSQL.9193.lock"
        ]
        
        for path in state_dirs:
            if os.path.exists(path):
                if os.path.isfile(path):
                    try:
                        os.remove(path)
                        logger.info(f"Removed state file: {path}")
                    except:
                        pass
                else:
                    try:
                        subprocess.run(["rm", "-rf", path], check=False)
                        logger.info(f"Cleaned state directory: {path}")
                    except:
                        pass
                        
        # Also clean any lock files
        subprocess.run(["find", "/tmp", "-name", "*.steampipe.lock", "-delete"], check=False, capture_output=True)
        
    except Exception as e:
        logger.error(f"Error cleaning Steampipe state: {e}")

def start_steampipe_service():
    """Start Steampipe service with proper cleanup and cloud-optimized settings"""
    try:
        # First, ensure no existing processes
        kill_steampipe_processes()

        # Clean state files
        clean_steampipe_state()

        # Wait a bit after cleanup
        time.sleep(3)

        # Check and install required plugins based on service type
        check_and_install_required_plugins()

        # Start service with cloud-optimized settings
        env = os.environ.copy()
        env['STEAMPIPE_DATABASE_START_TIMEOUT'] = '120'  # Increased timeout for cloud
        env['STEAMPIPE_LOG_LEVEL'] = 'info'
        env['STEAMPIPE_DATABASE_LISTEN'] = 'local'  # Force local binding
        env['STEAMPIPE_UPDATE_CHECK'] = 'false'
        env['POWERPIPE_UPDATE_CHECK'] = 'false'

        # Try to start the service - first try with alternative port if stuck
        logger.info("Starting Steampipe service with cloud-optimized settings...")

        # Check if default port is stuck
        port_check = subprocess.run(
            ["steampipe", "service", "status"],
            capture_output=True,
            text=True,
            timeout=30
        )

        # If we're in unknown state, try alternative port
        if "unknown state" in port_check.stderr or "unknown state" in port_check.stdout:
            logger.warning("Default port appears stuck, trying alternative port 9194...")
            result = subprocess.run(
                ["steampipe", "service", "start", "--database-listen=local", "--database-port=9194"],
                capture_output=True,
                text=True,
                timeout=120,  # Increased timeout
                env=env
            )

            if result.returncode == 0:
                logger.info("Started on alternative port 9194")
                # Update environment for this port
                os.environ['STEAMPIPE_DATABASE_PORT'] = '9194'
                # Wait longer for cloud initialization
                time.sleep(10)
                if check_steampipe_health():
                    return True

        # Try normal start with increased timeout
        logger.info("Attempting to start Steampipe on default port 9193...")
        result = subprocess.run(
            ["steampipe", "service", "start", "--database-listen=local", "--database-port=9193"],
            capture_output=True,
            text=True,
            timeout=120,  # Increased timeout for cloud
            env=env
        )

        logger.info(f"Steampipe start result: return_code={result.returncode}")
        if result.stdout:
            logger.info(f"Steampipe stdout: {result.stdout}")
        if result.stderr:
            logger.info(f"Steampipe stderr: {result.stderr}")

        # Check if we got the unknown state error
        if "unknown state" in result.stderr and "PID:" in result.stderr:
            # Extract PID from error message
            pid_match = re.search(r'PID:\s*(\d+)', result.stderr)
            if pid_match:
                pid = pid_match.group(1)
                logger.warning(f"Found stuck Steampipe process with PID {pid}")

            # Since we can't kill in Cloud Run, try working around it
            logger.warning("Unable to clean stuck process in containerized environment")
            logger.info("Attempting to use Steampipe in current state...")

            # Check if it's actually working despite the error
            if check_steampipe_health():
                logger.info("Steampipe appears to be working despite error message")
                return True

            # As last resort, return false to trigger complete reset
            return False
        
        if result.returncode != 0 and result.stderr:
            logger.error(f"Steampipe start failed: {result.stderr}")
            # Try to see if it's actually running anyway
            if check_steampipe_health():
                logger.info("Steampipe is running despite error")
                return True
            return False
        
        # Wait for service to be ready with progressive delays (cloud-optimized)
        logger.info("Waiting for Steampipe service to be ready...")
        for i in range(30):  # Increased attempts for cloud environment
            # Progressive delay: start with 2s, then 3s, then 5s for remaining attempts
            delay = min(2 + i // 10, 5)
            time.sleep(delay)

            if check_steampipe_health():
                logger.info(f"Steampipe service started successfully after {i+1} attempts")
                return True

            if i < 29:  # Don't log on the last attempt to avoid confusion
                logger.info(f"Health check attempt {i+1}/30... (waiting {delay}s)")

        logger.error("Steampipe service failed to become healthy after 30 attempts")
        return False
        
    except subprocess.TimeoutExpired:
        logger.error("Steampipe start command timed out")
        return False
    except Exception as e:
        logger.error(f"Failed to start Steampipe: {e}")
        return False

def restart_steampipe_service():
    """Restart Steampipe service with proper cleanup"""
    try:
        logger.info("Restarting Steampipe service...")
        
        # Stop service forcefully
        try:
            subprocess.run(
                ["steampipe", "service", "stop", "--force"],
                capture_output=True,
                text=True,
                timeout=30,
                check=False
            )
        except subprocess.TimeoutExpired:
            logger.warning("Stop command timed out, proceeding with cleanup")
        
        # Ensure processes are killed
        kill_steampipe_processes()
        
        # Start service
        return start_steampipe_service()
        
    except Exception as e:
        logger.error(f"Failed to restart Steampipe: {e}")
        return False

def ensure_steampipe_running():
    """Ensure Steampipe is running, restart if needed"""
    global _service_initialized

    with _service_lock:
        # If service was already initialized successfully, do a quick health check
        if _service_initialized:
            if check_steampipe_health():
                logger.info("Steampipe service is healthy")
                return
            else:
                logger.warning("Steampipe service was initialized but health check failed, reinitializing...")
                _service_initialized = False

        # Check current service status with improved error handling
        try:
            health_result = subprocess.run(
                ["steampipe", "service", "status"],
                capture_output=True,
                text=True,
                timeout=30
            )

            # Handle different status scenarios more gracefully
            if health_result.returncode == 0:
                # Service reports as running, verify with health check
                if check_steampipe_health():
                    logger.info("Steampipe service is running and healthy")
                    _service_initialized = True
                    return
                else:
                    logger.warning("Service reports running but health check failed, restarting...")

            # Handle unknown state more gracefully
            if "unknown state" in health_result.stdout or "unknown state" in health_result.stderr:
                logger.warning("Steampipe is in unknown state, attempting gentle restart...")
                if restart_steampipe_service():
                    _service_initialized = True
                    logger.info("Steampipe service restarted successfully")
                    return
                else:
                    logger.warning("Gentle restart failed, will try complete reset...")

        except subprocess.TimeoutExpired:
            logger.warning("Status check timed out, assuming service needs restart")
        except Exception as e:
            logger.warning(f"Status check failed: {e}, proceeding with startup")

        # Try normal startup first
        logger.info("Starting Steampipe service...")
        if start_steampipe_service():
            _service_initialized = True
            logger.info("Steampipe service started successfully")
            return

        # If normal startup fails, try one restart
        logger.warning("Normal startup failed, attempting restart...")
        if restart_steampipe_service():
            _service_initialized = True
            logger.info("Steampipe service restarted successfully")
            return

        # Last resort - complete reset
        logger.warning("All attempts failed, performing complete reset...")
        if reset_steampipe_completely():
            if start_steampipe_service():
                _service_initialized = True
                logger.info("Steampipe service started after complete reset")
                return

        raise Exception("Failed to start Steampipe service after all attempts including reset")

def reset_steampipe_completely():
    """Complete reset of Steampipe - nuclear option"""
    try:
        logger.warning("Performing complete Steampipe reset...")
        
        # Stop everything
        subprocess.run(["steampipe", "service", "stop", "--force"], check=False, capture_output=True, timeout=30)
        
        # Kill all processes
        subprocess.run(["pkill", "-9", "-f", "steampipe"], check=False, capture_output=True)
        subprocess.run(["pkill", "-9", "-f", "postgres"], check=False, capture_output=True)
        
        # Remove Steampipe data but preserve configuration files
        steampipe_dir = os.path.expanduser("~/.steampipe")
        if os.path.exists(steampipe_dir):
            # Backup configuration files from multiple locations
            config_dir = os.path.join(steampipe_dir, "config")
            local_config_dir = "steampipe-config"  # Local directory that might have configs
            config_files = {}

            # First try to backup from the main config directory
            if os.path.exists(config_dir):
                for filename in os.listdir(config_dir):
                    if filename.endswith('.spc'):
                        file_path = os.path.join(config_dir, filename)
                        try:
                            with open(file_path, 'r') as f:
                                config_files[filename] = f.read()
                            logger.info(f"Backed up configuration file: {filename}")
                        except Exception as e:
                            logger.warning(f"Failed to backup {filename}: {e}")

            # Also check local steampipe-config directory as fallback
            if os.path.exists(local_config_dir):
                for filename in os.listdir(local_config_dir):
                    if filename.endswith('.spc') and filename not in config_files:
                        file_path = os.path.join(local_config_dir, filename)
                        try:
                            with open(file_path, 'r') as f:
                                config_files[filename] = f.read()
                            logger.info(f"Backed up configuration file from local dir: {filename}")
                        except Exception as e:
                            logger.warning(f"Failed to backup from local dir {filename}: {e}")

            # Remove the entire directory (force removal of busy files)
            subprocess.run(["rm", "-rf", steampipe_dir], check=False)
            # Additional cleanup for stubborn files
            subprocess.run(["find", steampipe_dir, "-type", "f", "-delete"], check=False)
            subprocess.run(["find", steampipe_dir, "-type", "d", "-delete"], check=False)
            logger.info("Removed Steampipe directory")

            # Restore configuration files
            if config_files:
                os.makedirs(config_dir, exist_ok=True)
                for filename, content in config_files.items():
                    file_path = os.path.join(config_dir, filename)
                    try:
                        with open(file_path, 'w') as f:
                            f.write(content)
                        logger.info(f"Restored configuration file: {filename}")
                    except Exception as e:
                        logger.error(f"Failed to restore {filename}: {e}")
                logger.info("Restored all configuration files")
        
        # Wait before reinitializing
        time.sleep(5)
        
        # Reinitialize Steampipe
        logger.info("Reinitializing Steampipe...")
        subprocess.run(["steampipe", "plugin", "install", "steampipe"], check=False, capture_output=True, timeout=30)
        
        return True
    except Exception as e:
        logger.error(f"Failed to reset Steampipe: {e}")
        return False

def run_powerpipe_with_retry(command, max_retries=3, retry_delay=10):
    """
    Run powerpipe command with retry logic for database connection issues
    """
    import subprocess
    import time

    for attempt in range(max_retries):
        try:
            logger.info(f"Running powerpipe command (attempt {attempt + 1}/{max_retries}): {' '.join(command)}")

            # Determine working directory based on service type
            service_type = os.getenv('SERVICE_TYPE', 'unknown')
            if service_type == 'aws':
                cwd = "/app/steampipe-mod-aws-compliance"
            elif service_type == 'azure':
                cwd = "/app/steampipe-mod-azure-compliance"
            elif service_type == 'gcp':
                cwd = "/app/steampipe-mod-gcp-compliance"
            else:
                # Fallback: try to detect from command or use current directory
                if "aws" in str(command):
                    cwd = "/app/steampipe-mod-aws-compliance"
                elif "azure" in str(command):
                    cwd = "/app/steampipe-mod-azure-compliance"
                elif "gcp" in str(command):
                    cwd = "/app/steampipe-mod-gcp-compliance"
                else:
                    cwd = "/app"

            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=1800,  # 30 minutes timeout
                cwd=cwd
            )

            # Check for database connection errors
            if result.returncode != 0:
                error_output = result.stderr.lower()

                # Check for specific database connection errors
                connection_errors = [
                    "connection refused",
                    "dial tcp 127.0.0.1:9193: connect: connection refused",
                    "failed to connect to",
                    "database connection failed",
                    "no such host",
                    "connection timeout"
                ]

                is_connection_error = any(error in error_output for error in connection_errors)

                if is_connection_error and attempt < max_retries - 1:
                    logger.warning(f"Database connection error detected on attempt {attempt + 1}, retrying in {retry_delay} seconds...")
                    logger.warning(f"Error details: {result.stderr}")

                    # Try to restart Steampipe service
                    try:
                        restart_steampipe_service()
                        time.sleep(retry_delay)
                        continue
                    except Exception as e:
                        logger.error(f"Failed to restart Steampipe service: {e}")
                        time.sleep(retry_delay)
                        continue
                else:
                    # Not a connection error or max retries reached
                    logger.error(f"Command failed: {result.stderr}")
                    return result
            else:
                # Success
                logger.info("Powerpipe command completed successfully")
                return result

        except subprocess.TimeoutExpired:
            logger.error(f"Command timed out on attempt {attempt + 1}")
            if attempt < max_retries - 1:
                logger.info(f"Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
                continue
            else:
                raise
        except Exception as e:
            logger.error(f"Unexpected error on attempt {attempt + 1}: {e}")
            if attempt < max_retries - 1:
                logger.info(f"Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
                continue
            else:
                raise

    # If we get here, all retries failed
    logger.error(f"All {max_retries} attempts failed")
    return result

def initialize_steampipe_on_startup():
    """Initialize Steampipe service on container startup"""
    try:
        logger.info("Initializing Steampipe service on startup...")
        
        # First check and install required plugins based on service type
        if not check_and_install_required_plugins():
            logger.warning("Plugin installation had issues, but continuing...")
        
        # First try normal startup
        try:
            ensure_steampipe_running()
            logger.info("Steampipe service initialized successfully")
            return
        except Exception as e:
            logger.warning(f"Normal startup failed: {e}, trying complete reset...")
            
        # If normal startup fails, try complete reset
        if reset_steampipe_completely():
            # Try to start again after reset
            ensure_steampipe_running()
            logger.info("Steampipe service initialized successfully after reset")
        else:
            raise Exception("Failed to initialize Steampipe even after reset")
            
    except Exception as e:
        logger.error(f"Failed to initialize Steampipe on startup: {e}")
        # Don't fail container startup, but log the error
        # Service will be started on first request