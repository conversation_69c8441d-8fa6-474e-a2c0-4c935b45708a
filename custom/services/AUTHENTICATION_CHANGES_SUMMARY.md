# Multi-Cloud Authentication Changes Summary

## Overview

Updated authentication across all three cloud services (GCP, AWS, Azure) to support Secret Manager name parameter for deployment while maintaining local development credentials (ADC/profiles).

## Changes Applied to All Services

### 🔧 **Core Changes:**

1. **Updated Authentication Functions**:
   - Modified `get_service_credentials()` to accept `secret_manager_name` instead of `customer_id`
   - Added new `get_credentials_from_secret_manager()` function for each service
   - Changed default authentication type from 'sa' to 'adc' for better local development

2. **Updated Primary Benchmark Endpoints**:
   - **GCP**: `/api/gcp/run-benchmark-async`
   - **AWS**: `/api/aws/run-aws-benchmark-async`
   - **Azure**: `/api/azure/run-azure-benchmark-async`

3. **Updated Function Signatures**:
   - `run_powerpipe_command_async()`: Now takes `secret_manager_name` parameter
   - `run_powerpipe_command()`: Updated for consistency
   - Removed all `customer_id` parameter dependencies

## Service-Specific Details

### 🌐 **GCP Service**

**Primary Endpoint**: `/api/gcp/run-benchmark-async`

**Request Format:**
```json
{
  "benchmark": "cis_v300",
  "project_id": "your-project-id",
  "secret_manager_name": "your-secret-name"  // Optional for deployment
}
```

**Authentication Flow:**
- Local: Uses Application Default Credentials (ADC)
- Deployment: Uses Secret Manager with service account JSON

### ☁️ **AWS Service**

**Primary Endpoint**: `/api/aws/run-aws-benchmark-async`

**Request Format:**
```json
{
  "benchmark": "cis_v140",
  "account_id": "************",
  "secret_manager_name": "your-secret-name"  // Optional for deployment
}
```

**Authentication Flow:**
- Local: Uses AWS credentials file or environment variables
- Deployment: Uses Secret Manager with AWS access keys JSON

### 🔷 **Azure Service**

**Primary Endpoint**: `/api/azure/run-azure-benchmark-async`

**Request Format:**
```json
{
  "benchmark": "cis_v140",
  "subscription_id": "your-subscription-id",
  "secret_manager_name": "your-secret-name"  // Optional for deployment
}
```

**Authentication Flow:**
- Local: Uses Azure Default Credentials (ADC)
- Deployment: Uses Secret Manager with service principal JSON

## Secret Manager JSON Formats

### GCP Secret Format
```json
****************************************************************************************************************************************************************************************************************************************************************************************
```

### AWS Secret Format
```json
{
  "aws_access_key_id": "AKIA...",
  "aws_secret_access_key": "...",
  "region": "us-east-1",
  "session_token": "..."  // Optional
}
```

### Azure Secret Format
```json
{
  "client_id": "your-client-id",
  "client_secret": "your-client-secret",
  "tenant_id": "your-tenant-id"
}
```

## Environment Variables

- `AUTH_TYPE`: Set to 'adc' for local development (default)
- `PROJECT_ID`: GCP project ID for Secret Manager access (when using Secret Manager)

## Benefits

### ✅ **Unified Authentication**:
- Consistent API across all three cloud services
- Same parameter name (`secret_manager_name`) for all services
- Simplified deployment configuration

### ✅ **Better Local Development**:
- ADC/default credentials work seamlessly without configuration
- No need for customer_id headers or complex setup
- Automatic credential detection

### ✅ **Secure Deployment**:
- Secret Manager provides proper credential management
- No hardcoded credentials in environment variables
- Centralized secret management

### ✅ **Backward Compatibility**:
- Legacy functions preserved (though deprecated)
- Gradual migration supported
- Existing integrations continue to work

## Migration Guide

### For API Clients

1. **Remove Headers**: Remove `X-Customer-ID` headers from requests
2. **Add Secret Parameter**: Add `secret_manager_name` to request body for deployment
3. **Update Endpoints**: Some utility endpoints simplified (like `/api/gcp/get-org-id` remains GET)

### For Deployment

1. **Set up Secret Manager**: Store credentials in GCP Secret Manager
2. **Configure Environment**: Set `PROJECT_ID` environment variable
3. **Update Requests**: Include `secret_manager_name` in API calls

### Example Migration

**Before:**
```bash
curl -X POST /api/gcp/run-benchmark-async \
  -H "X-Customer-ID: customer123" \
  -d '{"benchmark": "cis_v300", "project_id": "proj-123"}'
```

**After:**
```bash
curl -X POST /api/gcp/run-benchmark-async \
  -d '{
    "benchmark": "cis_v300", 
    "project_id": "proj-123",
    "secret_manager_name": "gcp-credentials-secret"
  }'
```

## Testing

Each service includes the same authentication patterns and can be tested using:
- Local development: Omit `secret_manager_name` parameter
- Deployment: Include `secret_manager_name` parameter

The changes ensure consistent behavior across all three cloud platforms while maintaining the flexibility needed for both local development and production deployment.
