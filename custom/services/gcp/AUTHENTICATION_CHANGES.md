# GCP Service Authentication Changes

## Overview

The GCP service authentication has been updated to support both local development (using Application Default Credentials) and deployment (using Secret Manager) without requiring customer_id parameters.

## Changes Made

### 1. Updated Authentication Function

- Modified `get_service_credentials()` in `auth.py` to accept `secret_manager_name` instead of `customer_id`
- Added new function `get_credentials_from_secret_manager()` for Secret Manager authentication
- Default authentication type changed from 'sa' to 'adc' for better local development experience

### 2. Updated API Endpoints

#### `/api/gcp/run-benchmark-async` (Primary endpoint)
- **Before**: Required `customer_id` in headers
- **After**: Accepts optional `secret_manager_name` in request body

**Request Format:**
```json
{
  "benchmark": "cis_v300",
  "project_id": "your-project-id",
  "secret_manager_name": "your-secret-name"  // Optional for deployment
}
```

#### `/api/gcp/run-benchmark` (Synchronous endpoint)
- Same changes as async endpoint

#### `/api/gcp/test-auth`
- **Before**: Required `X-Customer-ID` header
- **After**: Accepts optional `secret_manager_name` in request body

#### `/api/gcp/get-org-id`
- **Before**: Required `X-Customer-ID` header
- **After**: Uses default credentials (ADC), no authentication parameters needed

### 3. Removed Dependencies

- Removed all references to `customer_id` parameters
- Removed `X-Customer-ID` header requirements
- Simplified authentication flow

## Usage

### Local Development (ADC)

For local development, simply omit the `secret_manager_name` parameter:

```bash
curl -X POST http://localhost:8080/api/gcp/run-benchmark-async \
  -H "Content-Type: application/json" \
  -d '{
    "benchmark": "cis_v300",
    "project_id": "your-project-id"
  }'
```

### Deployment (Secret Manager)

For deployment, include the `secret_manager_name` parameter:

```bash
curl -X POST http://localhost:8080/api/gcp/run-benchmark-async \
  -H "Content-Type: application/json" \
  -d '{
    "benchmark": "cis_v300",
    "project_id": "your-project-id",
    "secret_manager_name": "your-secret-name"
  }'
```

## Environment Variables

- `AUTH_TYPE`: Set to 'adc' for local development (default) or 'secret_manager' for deployment
- `PROJECT_ID`: GCP project ID for Secret Manager access (when using Secret Manager)

## Secret Manager Setup

When using Secret Manager authentication, ensure:

1. The secret exists in the specified project
2. The secret contains valid GCP service account JSON
3. The service account has necessary permissions for the target project
4. The application has permission to access the secret

## Testing

Use the provided test script to verify the changes:

```bash
cd custom/services/gcp
export TEST_PROJECT_ID="your-test-project"
export TEST_SECRET_NAME="your-test-secret"
python test_auth_changes.py
```

## Migration Notes

### For API Clients

1. Remove `X-Customer-ID` headers from requests
2. Add `secret_manager_name` to request body when deploying to `/api/gcp/run-benchmark-async`
3. `/api/gcp/get-org-id` remains a simple GET endpoint (no changes needed)

### For Deployment

1. Set up Secret Manager with service account credentials
2. Configure `PROJECT_ID` environment variable
3. Set `AUTH_TYPE=secret_manager` if needed (optional, auto-detected)

## Backward Compatibility

- Legacy customer_id functions are preserved but deprecated
- Old endpoints will continue to work but may log warnings
- Gradual migration is supported

## Security Improvements

1. No more customer_id exposure in headers
2. Secret Manager provides better credential management
3. ADC provides seamless local development experience
4. Reduced attack surface by removing unnecessary parameters
