# GCP Compliance Service
ARG BASE_IMAGE=steampipe-base:latest
FROM ${BASE_IMAGE}

USER root

# GCloud SDK is already in base image

# Copy shared custom code
COPY --chown=steampipe:steampipe custom/shared /app/shared

# Install Python dependencies
COPY --chown=steampipe:steampipe custom/services/gcp/requirements.txt /app/requirements.txt
RUN pip3 install --no-cache-dir -r /app/requirements.txt

# Copy the GCP compliance mod
COPY --chown=steampipe:steampipe steampipe-mod-gcp-compliance /app/steampipe-mod-gcp-compliance

# Apply any custom modifications (if they exist)

# Don't use wrapper - let powerpipe work naturally
# Copy custom Flask application and config
COPY --chown=steampipe:steampipe custom/services/gcp /app/gcp
# Copy all necessary files to /app for backward compatibility
COPY --chown=steampipe:steampipe custom/services/gcp/app.py /app/
COPY --chown=steampipe:steampipe custom/services/gcp/auth.py /app/
COPY --chown=steampipe:steampipe custom/services/gcp/generate_control.py /app/
COPY --chown=steampipe:steampipe custom/services/gcp/run_steampipe.py /app/
COPY --chown=steampipe:steampipe custom/services/gcp/steampipe-config /app/steampipe-config

USER steampipe

# Skip mod install for now - will be done at runtime if needed

ENV FLASK_APP=app.py
ENV SERVICE_TYPE=gcp

EXPOSE 8080

CMD ["python3", "-u", "/app/app.py"]