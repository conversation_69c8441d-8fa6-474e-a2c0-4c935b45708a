from flask import Blueprint, request, jsonify, abort
from googleapiclient.discovery import build
from google.auth import default
from google.oauth2 import service_account
from google.cloud import secretmanager
from functools import wraps
import os
import sys
import json
from google.auth.credentials import Credentials
import logging
import subprocess
import traceback
sys.path.append('/app/shared')
try:
    from secret_manager import SecretManagerClient, validate_credentials
except ImportError:
    # Fallback for local development
    SecretManagerClient = None
    validate_credentials = None


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create a blueprint
auth = Blueprint('auth', __name__)

# Use a writable directory for authentication files
# Override any imported value to ensure we use /tmp
AUTHENTICATION_FOLDER = '/tmp/authentication'
SERVICE_ACCOUNT_FILE = os.path.join(AUTHENTICATION_FOLDER, 'service-account.json')

def get_service_credentials(secret_manager_name=None):
    try:
        # Get authentication type from environment variables
        auth_type = os.getenv('AUTH_TYPE', 'adc').lower()  # Default to ADC for local development
        logger.info(f"Using authentication type: {auth_type}")

        # If secret_manager_name is provided, use Secret Manager authentication
        if secret_manager_name:
            logger.info(f"Using Secret Manager with secret name: {secret_manager_name}")
            creds, error = get_credentials_from_secret_manager(secret_manager_name)
            if creds:
                return creds, error
            else:
                return None, error
        elif auth_type == 'adc':
            try:
                logger.info("Attempting to use Application Default Credentials")
                credentials, project_id = default()
                logger.info(f"ADC Credentials path: {os.getenv('GOOGLE_APPLICATION_CREDENTIALS')}")
                logger.info(f"Credential type: {type(credentials).__name__}")
                logger.info(f"Valid credentials: {credentials.valid}")
                logger.info(f"Expiry: {credentials.expiry}")
                return credentials, project_id
            except Exception as e:
                logger.error(f"ADC authentication failed: {str(e)}")
                logger.error(f"Detailed error: {traceback.format_exc()}")
                return None, f"ADC authentication failed: {str(e)}"

        # If no secret manager name provided, fall back to default ADC
        logger.info("No secret manager name provided, using Application Default Credentials")
        try:
            credentials, project_id = default()
            logger.info(f"ADC Credentials path: {os.getenv('GOOGLE_APPLICATION_CREDENTIALS')}")
            logger.info(f"Credential type: {type(credentials).__name__}")
            logger.info(f"Valid credentials: {credentials.valid}")
            logger.info(f"Expiry: {credentials.expiry}")
            return credentials, project_id
        except Exception as e:
            logger.error(f"ADC authentication failed: {str(e)}")
            logger.error(f"Detailed error: {traceback.format_exc()}")
            return None, f"ADC authentication failed: {str(e)}"
            
    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        logger.error(f"Detailed error: {traceback.format_exc()}")
        return None, f"Authentication error: {str(e)}"

def get_credentials_from_secret_manager(secret_name):
    """Get GCP credentials from Secret Manager using secret name"""
    try:
        # Ensure we use the correct authentication folder
        global AUTHENTICATION_FOLDER
        AUTHENTICATION_FOLDER = '/tmp/authentication'

        # Get default credentials to access Secret Manager
        default_credentials, default_project_id = default()

        # Initialize Secret Manager client
        secret_client = secretmanager.SecretManagerServiceClient(credentials=default_credentials)

        # Get project ID from environment or use default
        project_id = os.getenv('PROJECT_ID') or default_project_id
        if not project_id:
            error_msg = "No project ID available for Secret Manager access"
            logger.error(error_msg)
            return None, error_msg

        # Construct the secret path
        name = f"projects/{project_id}/secrets/{secret_name}/versions/latest"

        try:
            response = secret_client.access_secret_version(request={"name": name})
            secret_content = response.payload.data.decode("UTF-8")
            logger.info(f"Successfully retrieved secret: {secret_name}")
        except Exception as e:
            logger.error(f"Failed to access secret: {str(e)}")
            logger.error(f"Secret path attempted: {name}")
            logger.error(f"Detailed error: {traceback.format_exc()}")
            return None, f"Failed to access secret: {str(e)}"

        try:
            service_account_json = json.loads(secret_content)
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in secret content: {str(e)}")
            return None, f"Invalid JSON in secret content: {str(e)}"

        # Write credentials to a temporary file for GCP SDK
        try:
            os.makedirs(AUTHENTICATION_FOLDER, mode=0o755, exist_ok=True)
        except Exception as e:
            logger.error(f"Failed to create authentication folder: {str(e)}")
            return None, f"Failed to create authentication folder: {str(e)}"

        # Create a unique file name for this secret
        file_path = os.path.join(AUTHENTICATION_FOLDER, f'service-account-{secret_name}.json')

        try:
            with open(file_path, 'w') as f:
                json.dump(service_account_json, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to write service account file: {str(e)}")
            return None, f"Failed to write service account file: {str(e)}"

        # Get project ID from credentials
        project_id = service_account_json.get('project_id')
        if not project_id:
            logger.error("project_id not found in service account JSON")
            return None, "project_id not found in service account JSON"

        # Create credentials object
        try:
            credentials = service_account.Credentials.from_service_account_file(file_path)

            logger.info(f"Successfully loaded GCP credentials from secret: {secret_name}, project: {project_id}")
            logger.info(f"Credential type: {type(credentials).__name__}")
            logger.info(f"Valid credentials: {credentials.valid}")
            logger.info(f"Expiry: {credentials.expiry}")

            return credentials, project_id

        except Exception as e:
            logger.error(f"Failed to create credentials from service account file: {str(e)}")
            return None, f"Failed to create credentials from service account file: {str(e)}"

    except Exception as e:
        logger.error(f"Error with Secret Manager authentication: {str(e)}")
        logger.error(f"Detailed error: {traceback.format_exc()}")
        return None, f"Secret Manager authentication failed: {str(e)}"

def get_customer_credentials_from_secret(customer_id):
    """Get customer-specific GCP credentials from Secret Manager (legacy function)"""
    try:
        # Ensure we use the correct authentication folder
        global AUTHENTICATION_FOLDER
        AUTHENTICATION_FOLDER = '/tmp/authentication'

        # Get project ID
        project_id = os.getenv('GCP_PROJECT_ID')
        if not project_id:
            credentials, project_id = default()

        # Initialize Secret Manager client
        sm_client = SecretManagerClient(project_id=project_id)

        # Get customer credentials
        creds, error = sm_client.get_customer_credentials(customer_id, 'gcp')
        if error:
            logger.error(f"Error getting customer credentials: {error}")
            return None, error

        # Validate credentials
        if validate_credentials:
            valid, validation_error = validate_credentials('gcp', creds)
            if not valid:
                logger.error(f"Invalid credentials: {validation_error}")
                return None, validation_error

        # Write credentials to a temporary file for GCP SDK
        try:
            os.makedirs(AUTHENTICATION_FOLDER, mode=0o755, exist_ok=True)
        except Exception as e:
            logger.error(f"Failed to create authentication folder: {str(e)}")
            return None, f"Failed to create authentication folder: {str(e)}"

        # Create a unique file name for this customer
        file_path = os.path.join(AUTHENTICATION_FOLDER, f'service-account-{customer_id}.json')

        try:
            with open(file_path, 'w') as f:
                json.dump(creds, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to write service account file: {str(e)}")
            return None, f"Failed to write service account file: {str(e)}"

        # Get project ID from credentials
        project_id = creds.get('project_id')
        if not project_id:
            logger.error("project_id not found in service account JSON")
            return None, "project_id not found in service account JSON"

        # Create credentials object
        try:
            credentials = service_account.Credentials.from_service_account_file(file_path)

            # Set environment variable for GCP SDK
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = file_path

            logger.info(f"Successfully loaded GCP credentials for customer {customer_id}, project: {project_id}")
            return credentials, project_id

        except Exception as e:
            logger.error(f"Failed to create credentials from service account file: {str(e)}")
            return None, f"Failed to create credentials from service account file: {str(e)}"

    except Exception as e:
        logger.error(f"Error with Secret Manager authentication: {str(e)}")
        logger.error(f"Detailed error: {traceback.format_exc()}")
        return None, f"Secret Manager authentication failed: {str(e)}"

