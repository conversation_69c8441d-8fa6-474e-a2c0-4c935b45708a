# AWS Compliance Service
ARG BASE_IMAGE=steampipe-base:latest
FROM ${BASE_IMAGE}

USER root

# AWS CLI already installed in base image

# Copy shared custom code
COPY --chown=steampipe:steampipe custom/shared /app/shared

# Install Python dependencies
COPY --chown=steampipe:steampipe custom/services/aws/requirements.txt /app/requirements.txt
RUN pip3 install --no-cache-dir -r /app/requirements.txt

# Note: Using real Powerpipe instead of wrapper script
# Powerpipe is already installed in the base image

# Copy the AWS compliance mod
COPY --chown=steampipe:steampipe steampipe-mod-aws-compliance /app/steampipe-mod-aws-compliance

# Copy all AWS service files
COPY --chown=steampipe:steampipe custom/services/aws/*.py /app/
COPY --chown=steampipe:steampipe custom/services/aws/steampipe-config /app/steampipe-config

USER steampipe

# Skip mod install for now - will be done at runtime if needed

ENV FLASK_APP=app.py
ENV SERVICE_TYPE=aws

EXPOSE 8082

CMD ["python3", "-u", "/app/app.py"]