from flask import Flask, request, jsonify, Response, stream_with_context
from flask_cors import CORS
import subprocess
import shlex
import logging
import sys
import json
import os
from datetime import datetime
import boto3
from botocore.exceptions import ClientError, NoCredentialsError, PartialCredentialsError
from generate_control import run_prompt
import re
import time
import random
from auth import auth, get_service_credentials
import run_steampipe
import signal
import atexit
from typing import List, Dict
import requests
from functools import wraps
import traceback
import threading
sys.path.append('/app/shared')
from job_manager import job_manager

# Set service type early to avoid plugin conflicts
os.environ['SERVICE_TYPE'] = 'aws'

try:
    from steampipe_health import initialize_steampipe_on_startup
except ImportError as e:
    print(f"Warning: Failed to import steampipe_health: {e}")
    initialize_steampipe_on_startup = None

# Fallback steampipe functions for when shared module fails
def basic_steampipe_start():
    """Basic steampipe service start without dependencies"""
    try:
        result = subprocess.run(
            ["steampipe", "service", "start"],
            capture_output=True,
            text=True,
            timeout=60
        )
        return result.returncode == 0
    except Exception as e:
        print(f"Basic steampipe start failed: {e}")
        return False

def basic_steampipe_health_check():
    """Basic steampipe health check without dependencies"""
    try:
        result = subprocess.run(
            ["steampipe", "query", "select 1 as test"],
            capture_output=True,
            text=True,
            timeout=30
        )
        return result.returncode == 0
    except Exception as e:
        print(f"Basic steampipe health check failed: {e}")
        return False


app = Flask(__name__)
CORS(app)

# Configure structured logging for Cloud Run
class StructuredMessage:
    def __init__(self, message, **kwargs):
        self.message = message
        self.kwargs = kwargs

    def __str__(self):
        return json.dumps({
            'message': self.message,
            'severity': 'INFO',
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            **self.kwargs
        })

# Configure logging for Cloud Run
logging.basicConfig(
    level=logging.INFO,
    format='%(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

def log(message, **kwargs):
    """Enhanced logging format for Cloud Run"""
    logger.info(StructuredMessage(message, **kwargs))

# Constants
CONTROLS_FILE_PATH = './my_controls/my_controls.pp'  # Path to your .sp file
domain = os.getenv('DOMAIN')
NEOSEC_VERIFICATION_URL = f"https://{domain}/charge_credits"
NEOSEC_SUBSCRIPTION_URL = f"https://{domain}/check_subscription"
AUTH_TYPE = os.getenv('AUTH_TYPE', 'adc')  # Default to 'adc' if not set

# AWS clients
aws_organizations = boto3.client('organizations')
aws_sts = boto3.client('sts')

 # Path to your .sp file

# Initialize global variables for process management
steampipe_process = None
max_retries = 3
database_timeout = 120  # seconds

def init_steampipe_service():
    """Initialize Steampipe service using improved health module"""
    try:
        if initialize_steampipe_on_startup is None:
            log("Steampipe health module not available, using basic initialization...", severity="WARNING")
            if basic_steampipe_start():
                log("Basic Steampipe service started successfully")
                # Wait a bit and check health
                time.sleep(5)
                if basic_steampipe_health_check():
                    log("Basic Steampipe health check passed")
                    return True
                else:
                    log("Basic Steampipe health check failed", severity="WARNING")
                    return True  # Still return True as service started
            else:
                log("Basic Steampipe service start failed", severity="ERROR")
                return False
        else:
            initialize_steampipe_on_startup()
            return True
    except Exception as e:
        log(f"Failed to initialize Steampipe service: {e}", severity="ERROR")
        return False

app.register_blueprint(auth)


# Helper Functions

def save_output_to_json(account_id, benchmark, result_data):
    """Save the command output and results to a JSON file."""
    try:
        # Create output directory if it doesn't exist
        output_dir = "/app/steampipe-mod-aws-compliance/outputs"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{output_dir}/{account_id}_{benchmark}_{timestamp}.json"
        
        # Prepare the JSON data
        json_data = {
            "account_id": account_id,
            "benchmark": benchmark,
            "timestamp": timestamp,
            "results": result_data
        }
        
        # Write JSON to file
        with open(filename, 'w') as f:
            json.dump(json_data, f, indent=2)
            
        return filename
    except Exception as e:
        log(f"Error saving output to JSON file: {str(e)}")
        return None

def run_powerpipe_command_async(job_id, command_name, account_id, secret_manager_name=None):
    """Execute powerpipe benchmark command asynchronously"""
    try:
        # Ensure Steampipe service is running before executing command
        log(f"Job {job_id}: Ensuring Steampipe service is ready...")
        try:
            # Set service type environment variable to avoid plugin conflicts
            os.environ['SERVICE_TYPE'] = 'aws'

            # Try to use advanced health module, fallback to basic
            try:
                from shared.steampipe_health import ensure_steampipe_running
                ensure_steampipe_running()
                log(f"Job {job_id}: Steampipe service verified as ready (advanced)")
            except ImportError:
                log(f"Job {job_id}: Using basic steampipe health check...", severity="WARNING")
                if not basic_steampipe_health_check():
                    log(f"Job {job_id}: Basic health check failed, attempting to start service...")
                    if not basic_steampipe_start():
                        raise Exception("Failed to start Steampipe service")
                    time.sleep(5)
                    if not basic_steampipe_health_check():
                        raise Exception("Steampipe service not responding after start")
                log(f"Job {job_id}: Steampipe service verified as ready (basic)")

        except Exception as e:
            error_msg = f"Failed to ensure Steampipe service is ready: {e}"
            log(error_msg, severity="ERROR")
            job_manager.update_job_status(job_id, 'failed', error=error_msg)
            return

        # If secret_manager_name is provided, reinitialize credentials
        if secret_manager_name:
            creds, actual_account_id = get_service_credentials(secret_manager_name)
            if creds is not None:  # Error occurred
                error_msg = f"Failed to get credentials from Secret Manager: {actual_account_id}"
                log(error_msg)
                job_manager.update_job_status(job_id, 'failed', error=error_msg)
                return
            log(f"Successfully authenticated with Secret Manager secret: {secret_manager_name}")

        # Update job status to running
        job_manager.update_job_status(job_id, 'running', progress=10)
        
        # Construct the command based on whether we want to scan all accounts or a specific one
        base_command = [
            "powerpipe",
            "benchmark",
            "run",
            command_name,
            "--var",
            f"account_id={account_id}",
            "--export=json"
        ]
        
        # For Secret Manager credentials, we use the default connection
        # Don't add search-path-prefix as it may not exist
        if account_id != "aws" and account_id != "all":
            connection_name = f"aws_{account_id}"
            base_command.extend(["--search-path-prefix", connection_name])
        
        log(f"Executing command: {' '.join(base_command)}")
        job_manager.update_job_status(job_id, 'running', progress=30)

        # Use retry mechanism for database connection issues
        try:
            from shared.steampipe_health import run_powerpipe_with_retry
            result = run_powerpipe_with_retry(base_command, max_retries=3, retry_delay=15)
            stdout = result.stdout
            stderr = result.stderr
            process_returncode = result.returncode
        except ImportError:
            log("Retry mechanism not available, using basic execution")
            # Fallback to original method
            process = subprocess.Popen(
                base_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                text=True,
                cwd="/app/steampipe-mod-aws-compliance"
            )
            stdout, stderr = process.communicate()
            process_returncode = process.returncode
        except Exception as e:
            log(f"Failed to execute command with retry: {e}")
            # Fallback to original method
            process = subprocess.Popen(
                base_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                text=True,
                cwd="/app/steampipe-mod-aws-compliance"
            )
            stdout, stderr = process.communicate()
            process_returncode = process.returncode

        job_manager.update_job_status(job_id, 'running', progress=80)

        # Try to find and read the exported JSON file
        json_file_path = None
        json_data = None
        
        for line in stdout.splitlines():
            if "File exported to" in line:
                json_file_path = line.split()[-1].strip()
                break
                
        if json_file_path and os.path.exists(json_file_path):
            try:
                with open(json_file_path, 'r') as f:
                    json_data = json.load(f)
                    
                # Save the result
                job_manager.save_result(job_id, 'aws', command_name, account_id, json_data)
                log(f"Job {job_id} completed successfully")
                
            except Exception as e:
                error_msg = f"Error reading JSON output: {str(e)}"
                log(error_msg)
                job_manager.update_job_status(job_id, 'failed', error=error_msg)
        else:
            # If no JSON file, save the raw output
            result = {
                'status': 'completed' if process_returncode == 0 else 'error',
                'stdout': stdout,
                'stderr': stderr,
                'return_code': process_returncode
            }

            if process_returncode == 0:
                job_manager.save_result(job_id, 'aws', command_name, account_id, result)
            else:
                job_manager.update_job_status(job_id, 'failed', error=stderr or stdout)
                
    except Exception as e:
        error_msg = f"Error executing benchmark: {str(e)}"
        log(error_msg)
        job_manager.update_job_status(job_id, 'failed', error=error_msg)

def run_powerpipe_command(command_name, account_id, secret_manager_name=None):
    """Execute Powerpipe benchmark command with account_id and return real-time output."""
    try:
        # If secret_manager_name is provided, reinitialize credentials
        if secret_manager_name:
            creds, actual_account_id = get_service_credentials(secret_manager_name)
            if creds is not None:  # Error occurred
                return {
                    "status": "error",
                    "error": f"Failed to get credentials from Secret Manager: {actual_account_id}",
                    "command": command_name
                }
        # Construct the command based on whether we want to scan all accounts or a specific one
        base_command = [
            "powerpipe",
            "benchmark",
            "run",
            command_name,
            "--var",
            f"account_id={account_id}",
            "--export=json"
        ]
        
        # Only add search-path-prefix if we have a specific account connection
        if account_id != "aws" and account_id != "all":
            # Check if specific connection exists
            connection_name = f"aws_{account_id}"
            base_command.extend(["--search-path-prefix", connection_name])
        
        log(f"Executing command: {' '.join(base_command)}")
        
        # Create a process with pipe to capture output
        process = subprocess.Popen(
            base_command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            text=True,
            cwd="/app/steampipe-mod-aws-compliance"
        )
        
        # Read output and error streams
        stdout, stderr = process.communicate()

        # Try to find and read the exported JSON file
        json_file_path = None
        json_data = None
        
        for line in stdout.splitlines():
            if "File exported to" in line:
                json_file_path = line.split()[-1].strip()
                break
                
        if json_file_path and os.path.exists(json_file_path):
            try:
                with open(json_file_path, 'r') as f:
                    json_data = json.load(f)
            except Exception as e:
                log(f"Error reading JSON file: {str(e)}")

        # Save consolidated results to our JSON file
        # Only include the actual benchmark results, not the raw stdout which may contain invalid characters
        result_data = {
            "benchmark_results": json_data,
            "command": command_name,
            "error": stderr if stderr else None
        }
        
        output_file = save_output_to_json(account_id, command_name, result_data)

        return {
            "status": "success" if process.returncode == 0 else "error",
            "output": stdout,
            "error": stderr if stderr else None,
            "command": command_name,
            "return_code": process.returncode,
            "output_file": output_file,
            "benchmark_results": json_data
        }
    except Exception as e:
        log(f"Error executing command: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "command": command_name
        }

def extract_steampipe_data(response_text: str, control_name_suggestion: str = "testcontrol") -> dict:
    """Extracts and formats the Steampipe control and query."""
    try:
        control_block = ""
        sql_query_block = ""
        control_name = control_name_suggestion

        # Extract the entire control block
        control_match = re.search(r'control\s+"([^"]+)"\s*\{[^}]*\}', response_text, re.DOTALL | re.IGNORECASE)
        if control_match:
            control_name = control_match.group(1).strip()
            control_block = control_match.group(0).strip()

        # Extract the entire query block
        query_match = re.search(r'query\s+"[^"]+"\s*\{[^}]*\}', response_text, re.DOTALL | re.IGNORECASE)
        if not query_match:  # If not in query block, look for a plain SQL block
           query_match = re.search(r'`sql(.*?)`', response_text, re.DOTALL | re.IGNORECASE)
           if query_match: # found sql block only
                sql_query_content = query_match.group(1).strip()
                # create query block
                sql_query_block = f'''query "{control_name}" {{
  sql = <<-EOQ
{sql_query_content}
  EOQ
}}'''
        else:
            sql_query_block = query_match.group(0).strip()

        # Construct default control block, if control block missing, but, sql block available
        if not control_block and sql_query_block:
            control_block = f"""control "{control_name}" {{
  title       = "Generated Control for {control_name}"
  description = "Generated control based on user prompt."
  query       = query.{control_name}
  tags = merge(local.aws_compliance_common_tags, {{
    service = "AWS/Generated"  // Modify as needed
  }})
}}"""

        if not control_block or not sql_query_block: #check if we have both blocks
            return {"error": "Could not find both control and query blocks."}

        return {
            "control_block": control_block,
            "query_block": sql_query_block,
            "control_name": control_name,
        }

    except Exception as e:
        return {"error": f"Error during extraction/formatting: {e}"}

def write_steampipe_control_file(filepath: str, extracted_data: dict) -> None:
    """Overwrites the Steampipe control file with the new content."""
    control_name = extracted_data.get('control_name', 'testcontrol')  # Fallback
    control_block = extracted_data.get('control_block')
    query_block = extracted_data.get('query_block')

    if not control_block or not query_block:
        raise ValueError("Control block and query block are required.")

    try:
        # Construct the complete file content
        new_file_content = f"""locals {{
  my_controls_common_tags = merge(local.aws_compliance_common_tags, {{
    type = "Benchmark"
  }})
}}

benchmark "my_controls" {{
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.{control_name},
  ]
  tags = local.my_controls_common_tags
}}

{control_block}

{query_block}
"""
        # Write the new content to the file (overwrites existing content)
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(new_file_content)

    except Exception as e:
        raise Exception(f"Error writing to Steampipe control file: {e}")

# AWS Organization and Account Functions
def get_aws_organization_id():
    """Retrieve the AWS organization ID using Boto3."""
    try:
        # Create a new session to ensure it picks up the latest credentials
        session = boto3.session.Session()
        organizations = session.client('organizations', region_name='us-east-1')
        
        response = organizations.describe_organization()
        return response['Organization']['Id'], None
    except ClientError as e:
        log(f"AWS API Error: {e}")
        # Check for specific credential errors
        error_code = getattr(e, 'response', {}).get('Error', {}).get('Code', '')
        error_message = getattr(e, 'response', {}).get('Error', {}).get('Message', str(e))
        
        if error_code in ['InvalidClientTokenId', 'UnrecognizedClientException', 'AccessDenied']:
            return None, f"AWS Credentials Error: {error_message}"
        return None, f"AWS API Error: {error_message}"
    except NoCredentialsError:
        log("No AWS credentials found")
        return None, "No AWS credentials found"
    except PartialCredentialsError as e:
        log(f"Partial AWS credentials error: {e}")
        return None, f"Partial AWS credentials error: {e}"
    except Exception as e:
        log(f"Unexpected error in get_aws_organization_id: {str(e)}")
        return None, f"Unexpected error: {str(e)}"

def get_aws_accounts():
    """Retrieve all AWS accounts in the organization."""
    try:
        # Create a new session to ensure it picks up the latest credentials
        session = boto3.session.Session()
        organizations = session.client('organizations', region_name='us-east-1')
        
        accounts = []
        paginator = organizations.get_paginator('list_accounts')
        for page in paginator.paginate():
            accounts.extend(page['Accounts'])
        return accounts, None
    except ClientError as e:
        log(f"AWS API Error: {e}")
        # Check for specific credential errors
        error_code = getattr(e, 'response', {}).get('Error', {}).get('Code', '')
        error_message = getattr(e, 'response', {}).get('Error', {}).get('Message', str(e))
        
        if error_code in ['InvalidClientTokenId', 'UnrecognizedClientException', 'AccessDenied']:
            return None, f"AWS Credentials Error: {error_message}"
        return None, f"AWS API Error: {error_message}"
    except NoCredentialsError:
        log("No AWS credentials found")
        return None, "No AWS credentials found"
    except PartialCredentialsError as e:
        log(f"Partial AWS credentials error: {e}")
        return None, f"Partial AWS credentials error: {e}"
    except Exception as e:
        log(f"Unexpected error in get_aws_accounts: {str(e)}")
        return None, f"Unexpected error: {str(e)}"

def check_subscription():
    """
    Decorator to verify active subscription status without charging credits.
    Similar to charge_credits but uses the check_subscription endpoint instead.
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if AUTH_TYPE == 'adc':
                log("Local development detected, skipping subscription verification", 
                    auth_type=AUTH_TYPE,
                    severity="INFO")
                return f(*args, **kwargs)
            try:
                # Get IAP token from headers
                iap_token = request.headers.get('X-Original-Iap-Jwt')
                log("Processing request headers", 
                    headers=dict(request.headers),
                    severity="DEBUG")
                
                host = request.headers.get('Referer')
                if not iap_token:
                    # For now, allow access without IAP token
                    log("No IAP token provided, allowing access (IAP not enforced)", severity="INFO")
                    return f(*args, **kwargs)

                # Make request to check_subscription endpoint on neosec marketplace
                headers = {
                    "token": iap_token,
                    "Content-Type": "application/json",
                    "subdomain": host
                }
                
                log("Making request to check subscription", 
                    url=NEOSEC_SUBSCRIPTION_URL,  # You'll need to define this constant
                    severity="DEBUG")
                    
                # No need to include cost_credits for subscription check
                data = {}
                
                response = requests.post(
                    NEOSEC_SUBSCRIPTION_URL,
                    headers=headers,
                    json=data
                )

                log("Response from subscription check", 
                    response_status=response.status_code,
                    response_body=response.text,
                    severity="DEBUG")
                
                if response.status_code != 200:
                    log("Subscription verification failed", 
                        status_code=response.status_code,
                        severity="ERROR")
                    return jsonify({
                        "status": "error",
                        "error": "No active subscription found"
                    }), 402  # Payment Required

                return f(*args, **kwargs)
            except Exception as e:
                log("Subscription verification error",
                    error=str(e),
                    stack_trace=traceback.format_exc(),
                    severity="ERROR")
                # For now, allow access on error
                log("Allowing access despite subscription check error", severity="INFO")
                return f(*args, **kwargs)
        return decorated_function
    return decorator

def charge_credits(cost_credits=1):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if AUTH_TYPE == 'adc':
                log("Local development detected, skipping IAP verification", 
                    auth_type=AUTH_TYPE,
                    severity="INFO")
                return f(*args, **kwargs)
            try:
                # Get IAP token from headers
                iap_token = request.headers.get('X-Original-Iap-Jwt')
                log("Processing request headers", 
                    headers=dict(request.headers),
                    severity="DEBUG")
                
                host = request.headers.get('Referer')
                if not iap_token:
                    # For now, allow access without IAP token
                    log("No IAP token provided, allowing access (IAP not enforced)", severity="INFO")
                    return f(*args, **kwargs)

                # Make request to charge_credit endpoint on neosec marketplace
                headers = {
                    "token": iap_token,
                    "Content-Type": "application/json",
                    "subdomain": host
                }
                
                log("Making request to Neosec marketplace", 
                    url=NEOSEC_VERIFICATION_URL,
                    credits=cost_credits,
                    severity="DEBUG")
                    
                data = {
                    "cost_credits": cost_credits
                }
                
                response = requests.post(
                    NEOSEC_VERIFICATION_URL,
                    headers=headers,
                    json=data
                )

                log("Response from charge credit", 
                    response_status=response.status_code,
                    response_body=response.text,
                    severity="DEBUG")
                
                if response.status_code != 200:
                    log("Failed to charge credit", 
                        status_code=response.status_code,
                        severity="ERROR")
                    return jsonify({
                        "status": "error",
                        "error": "Failed to charge credit"
                    }), 400

                return f(*args, **kwargs)
            except Exception as e:
                log("IAP verification error",
                    error=str(e),
                    stack_trace=traceback.format_exc(),
                    severity="ERROR")
                # For now, allow access on error
                log("Allowing access despite IAP check error", severity="INFO")
                return f(*args, **kwargs)
        return decorated_function
    return decorator

        
@app.route('/api/aws/generate', methods=['POST'])
def generate():
    """API endpoint to generate control."""
    try:
        data = request.get_json()
        user_query = data['prompt']  # Get the prompt from the request

        response_text = run_prompt(user_query)
        extracted_data = extract_steampipe_data(response_text)

        if "error" in extracted_data:
            return jsonify({'error': extracted_data["error"], 'response': response_text}), 500

        write_steampipe_control_file(CONTROLS_FILE_PATH, extracted_data)

        # Return the combined, formatted output to the user.
        formatted_output = f"{extracted_data['control_block']}\n\n{extracted_data['query_block']}"
        return jsonify({'response': formatted_output})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/aws/testcontrol', methods=['POST'])
def testcontrol():
    """
    Runs a Powerpipe command.  Expects 'command_name' and 'account_id' in the request body.
    """
    data = request.get_json()

    if not data or 'account_id' not in data:
        account_id = 'aws'
    else:
        account_id = data['account_id']
    try:
        output = run_powerpipe_command("my_controls", account_id)
        log(f"{output}")
        # Return the output as a JSON response, and set the content type to plain text.
        return jsonify({'response': output})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/aws/run-aws-benchmark', methods=['POST'])
@check_subscription()
def run_benchmark():
    """API endpoint to run Powerpipe benchmark."""
    try:
        data = request.get_json()
        if not data or 'benchmark' not in data:
            return jsonify({
                "status": "error",
                "error": "Missing 'benchmark' parameter"
            }), 400

        if 'account_id' not in data:
            return jsonify({
                "status": "error",
                "error": "Missing 'account_id' parameter"
            }), 400

        benchmark = data['benchmark']
        account_id = data['account_id']
        secret_manager_name = data.get('secret_manager_name')  # Optional parameter for deployment

        log(f"Running benchmark: {benchmark} for account: {account_id}")
        if secret_manager_name:
            log(f"Using Secret Manager authentication with secret: {secret_manager_name}")
        else:
            log("Using local AWS credentials")
        
        # Check if we should use streaming (for known large responses)
        should_stream = (benchmark == "nist_800_53_rev_5" and account_id == "aws")
        
        if should_stream:
            # Stream large responses
            def generate():
                # Run the benchmark and get results
                result = run_powerpipe_command(benchmark, account_id, secret_manager_name)
                
                # Stream the result in chunks
                # First send metadata/header
                yield json.dumps({"status": "streaming", "total_size": len(json.dumps(result))}) + "\n"
                
                # Then stream the benchmark results in chunks
                chunk_size = 1024 * 1024  # 1MB chunks
                result_json = json.dumps(result)
                
                for i in range(0, len(result_json), chunk_size):
                    chunk = result_json[i:i+chunk_size]
                    yield chunk
            
            return Response(stream_with_context(generate()), 
                            content_type='application/json; charset=utf-8')
        else:
            # For smaller responses, keep existing behavior
            result = run_powerpipe_command(benchmark, account_id, secret_manager_name)
            return jsonify(result)
        
    except Exception as e:
        log(f"API error: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/api/aws/run-aws-benchmark-async', methods=['POST'])
@check_subscription()
def run_benchmark_async():
    """API endpoint to run powerpipe benchmark asynchronously"""
    try:
        data = request.get_json()
        if not data or 'benchmark' not in data:
            return jsonify({
                "status": "error",
                "error": "Missing 'benchmark' parameter"
            }), 400

        if 'account_id' not in data:
            return jsonify({
                "status": "error",
                "error": "Missing 'account_id' parameter"
            }), 400

        benchmark = data['benchmark']
        account_id = data['account_id']
        secret_manager_name = data.get('secret_manager_name')  # Optional parameter for deployment

        log(f"Running benchmark: {benchmark} for account: {account_id}")
        if secret_manager_name:
            log(f"Using Secret Manager authentication with secret: {secret_manager_name}")
        else:
            log("Using local AWS credentials (ADC/profile)")

        # Ensure Steampipe service is running before starting benchmark
        log("Ensuring Steampipe service is ready before benchmark execution...")
        try:
            # Set service type environment variable to avoid plugin conflicts
            os.environ['SERVICE_TYPE'] = 'aws'

            # Try to use advanced health module, fallback to basic
            try:
                from shared.steampipe_health import ensure_steampipe_running
                ensure_steampipe_running()
                log("Steampipe service verified as ready (advanced)")
            except ImportError:
                log("Using basic steampipe health check...", severity="WARNING")
                if not basic_steampipe_health_check():
                    log("Basic health check failed, attempting to start service...")
                    if not basic_steampipe_start():
                        raise Exception("Failed to start Steampipe service")
                    time.sleep(5)
                    if not basic_steampipe_health_check():
                        raise Exception("Steampipe service not responding after start")
                log("Steampipe service verified as ready (basic)")

        except Exception as e:
            log(f"Failed to ensure Steampipe service is ready: {e}", severity="ERROR")
            return jsonify({
                "status": "error",
                "error": "Steampipe service is not available"
            }), 503

        # Create a job
        job_id = job_manager.create_job('aws', benchmark, account_id)

        # Run the benchmark in a background thread
        thread = threading.Thread(
            target=run_powerpipe_command_async,
            args=(job_id, benchmark, account_id, secret_manager_name),
            daemon=True
        )
        thread.start()

        log(f"Started async benchmark job: {job_id}")

        return jsonify({
            "status": "success",
            "job_id": job_id,
            "message": "Benchmark job started",
            "check_status_url": f"/api/aws/job/{job_id}/status",
            "get_result_url": f"/api/aws/job/{job_id}/result"
        })

    except Exception as e:
        log(f"API error: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/api/aws/job/<job_id>/status', methods=['GET'])
def get_job_status(job_id):
    """Get the status of a running job"""
    job_status = job_manager.get_job_status(job_id)
    
    if not job_status:
        return jsonify({
            "status": "error",
            "error": "Job not found"
        }), 404
    
    return jsonify(job_status)

@app.route('/api/aws/job/<job_id>/result', methods=['GET'])
def get_job_result(job_id):
    """Get the result of a completed job"""
    job_status = job_manager.get_job_status(job_id)
    
    if not job_status:
        return jsonify({
            "status": "error",
            "error": "Job not found"
        }), 404
    
    if job_status['status'] != 'completed':
        return jsonify({
            "status": "error",
            "error": f"Job is {job_status['status']}, not completed",
            "job_status": job_status
        }), 400
    
    result = job_manager.get_result_by_job_id(job_id)
    if result:
        return jsonify(result)
    else:
        return jsonify({
            "status": "error",
            "error": "Result not found"
        }), 404

@app.route('/api/aws/latest-result/<benchmark>/<account_id>', methods=['GET'])
def get_latest_result(benchmark, account_id):
    """Get the latest result for a specific benchmark and account"""
    result = job_manager.get_latest_result('aws', benchmark, account_id)
    
    if result:
        return jsonify(result)
    else:
        return jsonify({
            "status": "error",
            "error": "No results found for this benchmark and account"
        }), 404

@app.route('/api/aws/list-results', methods=['GET'])
def list_results():
    """List all available results"""
    results = job_manager.list_available_results('aws')
    return jsonify({
        "status": "success",
        "results": results
    })

@app.route('/api/aws/jobs', methods=['GET'])
def list_jobs():
    """List all jobs"""
    jobs = job_manager.get_all_jobs('aws')
    return jsonify({
        "status": "success",
        "jobs": jobs
    })

@app.route('/api/aws/benchmark', methods=['POST'])
@check_subscription()
def run_benchmark_alias():
    """Alias endpoint for run-aws-benchmark-async"""
    return run_benchmark_async()

@app.route('/api/aws/reports', methods=['GET'])
def list_reports():
    """List all reports for a customer"""
    try:
        # List all saved report files in the outputs directory
        output_dir = "/app/steampipe-mod-aws-compliance/outputs"
        reports = []
        
        if os.path.exists(output_dir):
            for filename in os.listdir(output_dir):
                if filename.endswith('.json'):
                    # Parse filename to extract details
                    parts = filename.replace('.json', '').split('_')
                    if len(parts) >= 3:
                        account_id = parts[0]
                        benchmark = '_'.join(parts[1:-2])  # Handle benchmarks with underscores
                        timestamp = parts[-2] + '_' + parts[-1]
                        
                        # Get file size and modification time
                        file_path = os.path.join(output_dir, filename)
                        file_stat = os.stat(file_path)
                        
                        reports.append({
                            "filename": filename,
                            "account_id": account_id,
                            "benchmark": benchmark,
                            "timestamp": timestamp,
                            "size": file_stat.st_size,
                            "modified": datetime.fromtimestamp(file_stat.st_mtime).isoformat()
                        })
        
        # Sort by modification time, most recent first
        reports.sort(key=lambda x: x['modified'], reverse=True)
        
        return jsonify({
            "status": "success",
            "reports": reports,
            "total": len(reports)
        })
    except Exception as e:
        log(f"Error listing reports: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/api/aws/listReports/<account_id>', methods=['GET'])
def list_reports_by_account(account_id):
    """List saved report files for a specific AWS account"""
    try:
        # List all saved report files for the specified account
        output_dir = "/app/steampipe-mod-aws-compliance/outputs"
        reports = []
        
        if os.path.exists(output_dir):
            for filename in os.listdir(output_dir):
                if filename.startswith(f"{account_id}_") and filename.endswith('.json'):
                    # Parse filename to extract details
                    parts = filename.replace('.json', '').split('_')
                    if len(parts) >= 3:
                        benchmark = '_'.join(parts[1:-2])  # Handle benchmarks with underscores
                        timestamp = parts[-2] + '_' + parts[-1]
                        
                        # Get file size and modification time
                        file_path = os.path.join(output_dir, filename)
                        file_stat = os.stat(file_path)
                        
                        reports.append({
                            "filename": filename,
                            "account_id": account_id,
                            "benchmark": benchmark,
                            "timestamp": timestamp,
                            "size": file_stat.st_size,
                            "modified": datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                            "path": file_path
                        })
        
        # Sort by modification time, most recent first
        reports.sort(key=lambda x: x['modified'], reverse=True)
        
        return jsonify({
            "status": "success",
            "account_id": account_id,
            "reports": reports,
            "total": len(reports)
        })
    except Exception as e:
        log(f"Error listing reports for account {account_id}: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/api/aws/get-aws-org-id', methods=['GET'])
def get_organization_id():
    """API endpoint to fetch AWS organization ID."""
    try:
        # Attempt to re-initialize AWS credentials if needed
        try:
            session = boto3.session.Session(region_name='us-east-1')
            log("Created new boto3 session for organization ID request")
        except Exception as e:
            log(f"Error creating boto3 session: {str(e)}")
        
        org_id, error = get_aws_organization_id()
        if error:
            log(f"Organization ID fetch error: {error}")
            return jsonify({"status": "error", "error": error}), 500
            
        return jsonify({
            "status": "success",
            "organization_id": org_id
        })
    except Exception as e:
        log(f"Organization ID API error: {str(e)}")
        return jsonify({
            "status": "error", 
            "error": str(e)
        }), 500 

@app.route('/api/aws/get-accounts', methods=['POST'])
def get_accounts():
    """API endpoint to fetch all AWS accounts in the organization."""
    try:
        # Attempt to re-initialize AWS credentials if needed
        try:
            session = boto3.session.Session(region_name='us-east-1')
            log("Created new boto3 session for accounts request")
        except Exception as e:
            log(f"Error creating boto3 session: {str(e)}")
        
        data = request.get_json()
        if not data or 'organization_id' not in data:
            return jsonify({"status": "error", "error": "Missing organization_id"}), 400

        accounts, error = get_aws_accounts()
        if error:
            log(f"Accounts fetch error: {error}")
            return jsonify({"status": "error", "error": error}), 500

        # Transform AWS accounts to match GCP projects format
        formatted_projects = [
            {
                "project_name": account["Name"],
                "name": account["Id"],
                "project_number": account["Id"]  # Using account ID as number for consistency
            }
            for account in accounts
        ]

        return jsonify({
            "status": "success",
            "projects": formatted_projects  # Changed from 'accounts' to 'projects'
        })
    except Exception as e:
        log(f"Error in get-accounts API: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Basic endpoint to verify API is running."""
    return jsonify({
        "status": "running"
    })

def main():
    """Main application entry point"""
    global credential, account_id, aws_organizations, aws_sts, AUTH_TYPE

    log("Starting application initialization...")

    # Set service type environment variable early
    os.environ['SERVICE_TYPE'] = 'aws'

    # Initialize credentials first
    log("Initializing credentials...")
    credential, account_id = get_service_credentials()
    log(f"Credentials initialized. Project ID: {account_id}")

    # Re-initialize AWS clients with fresh session to ensure they use the correct credentials
    try:
        session = boto3.session.Session(region_name='us-east-1')
        aws_organizations = session.client('organizations')
        aws_sts = session.client('sts')
        log("AWS clients initialized with fresh session")
    except Exception as e:
        log(f"Warning: Error initializing AWS clients: {str(e)}")

    # Generate Steampipe configuration with credentials
    log("Generating Steampipe configuration...")
    is_cloud_run = os.getenv('K_SERVICE') is not None
    config_path = run_steampipe.generate_and_save_config(
        is_cloud_run=is_cloud_run# Pass org_id if available, or None
    )
    
    # Start Steampipe service with three attempts
    if not init_steampipe_service():
       log("CRITICAL: Failed to start Steampipe service after 3 attempts. Exiting.")
       sys.exit(1)
    
    # Start Flask application
    port = int(os.getenv('PORT', 8080))
    log(f"Starting Flask application on port {port}...")
    app.run(host='0.0.0.0', port=port, debug=False)

if __name__ == '__main__':
    main()