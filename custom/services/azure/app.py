from flask import Flask, request, jsonify
from flask_cors import CORS
import subprocess
import shlex
import logging
import sys
import json
import os
import signal
import atexit
from datetime import datetime
from azure.core.exceptions import AzureError
from azure.identity import DefaultAzureCredential
from azure.mgmt.resource import ResourceManagementClient
from azure.mgmt.subscription import SubscriptionClient
from generate_control import run_prompt
import re
import time
import random
from auth import auth, get_service_credentials  # Import auth module
import run_steampipe  # Import the run_steampipe module
import traceback
import requests
from functools import wraps
import threading
sys.path.append('/app/shared')
from job_manager import job_manager

# Set service type early to avoid plugin conflicts
os.environ['SERVICE_TYPE'] = 'azure'

try:
    from steampipe_health import initialize_steampipe_on_startup
except ImportError as e:
    print(f"Warning: Failed to import steampipe_health: {e}")
    initialize_steampipe_on_startup = None

# Initialize the Flask app
app = Flask(__name__)
CORS(app)
app.register_blueprint(auth)  # Register auth blueprint

# Configure structured logging for Cloud Run
class StructuredMessage:
    def __init__(self, message, **kwargs):
        self.message = message
        self.kwargs = kwargs

    def __str__(self):
        return json.dumps({
            'message': self.message,
            'severity': 'INFO',
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            **self.kwargs
        })

# Configure logging for Cloud Run
logging.basicConfig(
    level=logging.INFO,
    format='%(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

def log(message, **kwargs):
    """Enhanced logging format for Cloud Run"""
    logger.info(StructuredMessage(message, **kwargs))

# Constants
CONTROLS_FILE_PATH = './my_controls/my_controls.pp'  # Path to your .sp file
domain = os.getenv('DOMAIN')
NEOSEC_VERIFICATION_URL = f"https://{domain}/charge_credits"
NEOSEC_SUBSCRIPTION_URL = f"https://{domain}/check_subscription"
AUTH_TYPE = os.getenv('AUTH_TYPE', 'adc')  # Default to 'adc' if not set

# Initialize global variables for credentials and process management
credentials = None
subscription_id = None
steampipe_process = None
max_retries = 3
database_timeout = 120  # seconds

def init_steampipe_service():
    """Initialize Steampipe service using improved health module"""
    try:
        if initialize_steampipe_on_startup is None:
            log("Steampipe health module not available, attempting basic initialization...", severity="WARNING")
            # Try basic steampipe service start
            result = subprocess.run(
                ["steampipe", "service", "start"],
                capture_output=True,
                text=True,
                timeout=60
            )
            if result.returncode == 0:
                log("Basic Steampipe service started successfully")
                return True
            else:
                log(f"Basic Steampipe service start failed: {result.stderr}", severity="ERROR")
                return False
        else:
            initialize_steampipe_on_startup()
            return True
    except Exception as e:
        log(f"Failed to initialize Steampipe service: {e}", severity="ERROR")
        return False



def get_azure_tenant_id():
    """Retrieve the Azure tenant ID using credentials or environment variables"""
    global credentials
    
    try:
        if not credentials:
            return None, "No credentials available to get tenant ID"

        # Initialize subscription client
        subscription_client = SubscriptionClient(credentials)
        
        tenants = list(subscription_client.tenants.list())
        if not tenants:
            return None, "No tenants found"
            
        tenant_info = {
            "tenant_id": tenants[0].tenant_id,
            "tenant_category": tenants[0].tenant_category if hasattr(tenants[0], 'tenant_category') else "Unknown",
            "country": tenants[0].country if hasattr(tenants[0], 'country') else "Unknown",
            "all_tenants": [
                {
                    "tenant_id": tenant.tenant_id,
                    "tenant_category": tenant.tenant_category if hasattr(tenant, 'tenant_category') else "Unknown",
                    "country": tenant.country if hasattr(tenant, 'country') else "Unknown"
                } for tenant in tenants
            ]
        }
        
        return tenant_info, None
    except Exception as e:
        log(f"Error getting tenant information: {str(e)}")
        return None, f"Error retrieving tenant information: {str(e)}"

def get_azure_subscriptions(credentials=None, tenant_id=None):
    """
    Get Azure subscriptions using provided credentials
    Returns a list of subscription objects
    """
    try:    
        # Try to get subscriptions via API
        subscription_client = SubscriptionClient(credentials)
        subscriptions = []
        for subscription in subscription_client.subscriptions.list():
            subscriptions.append({
                "subscription_id": subscription.subscription_id,  # Use consistent naming format
                "name": subscription.display_name,               # Use consistent naming format
                "state": subscription.state
            })

        return subscriptions
    except AzureError as e:
        print(f"Error fetching Azure subscriptions: {e}")
        return []
    except Exception as e:
        print(f"Unexpected error: {e}")
        return []

def save_output_to_json(subscription_id, benchmark, result_data):
    """Save the command output and results to a JSON file."""
    try:
        # Create output directory if it doesn't exist
        output_dir = "/app/steampipe-mod-azure-compliance/outputs"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{output_dir}/{subscription_id}_{benchmark}_{timestamp}.json"
        
        # Prepare the JSON data
        json_data = {
            "subscription_id": subscription_id,
            "benchmark": benchmark,
            "timestamp": timestamp,
            "results": result_data
        }
        
        # Write JSON to file
        with open(filename, 'w') as f:
            json.dump(json_data, f, indent=2)
            
        return filename
    except Exception as e:
        log(f"Error saving output to JSON file: {str(e)}")
        return None

def run_powerpipe_command_async(job_id, command_name, subscription_id, secret_manager_name=None):
    """Execute powerpipe benchmark command asynchronously"""
    try:
        # Ensure Steampipe service is running before executing command
        log(f"Job {job_id}: Ensuring Steampipe service is ready...")
        try:
            from shared.steampipe_health import ensure_steampipe_running
            ensure_steampipe_running()
            log(f"Job {job_id}: Steampipe service verified as ready")
        except Exception as e:
            error_msg = f"Failed to ensure Steampipe service is ready: {e}"
            log(error_msg, severity="ERROR")
            job_manager.update_job_status(job_id, 'failed', error=error_msg)
            return

        # If secret_manager_name is provided, reinitialize credentials
        if secret_manager_name:
            creds, error = get_service_credentials(secret_manager_name)
            if creds is None:  # Error occurred
                error_msg = f"Failed to get credentials from Secret Manager: {error}"
                log(error_msg)
                job_manager.update_job_status(job_id, 'failed', error=error_msg)
                return
            log(f"Successfully authenticated with Secret Manager secret: {secret_manager_name}")

        # Update job status to running
        job_manager.update_job_status(job_id, 'running', progress=10)
        
        # Replace hyphens with underscores in subscription_id for path formatting
        formatted_subscription_id = subscription_id.replace("-", "_")
        
        # Construct the command based on whether we want to scan all subscriptions or a specific one
        base_command = [
            "powerpipe",
            "benchmark",
            "run",
            command_name,
            "--var",
            f"subscription_id={subscription_id}",
            "--export=json"
        ]
        
        # For Secret Manager credentials, we use the default connection
        # Don't add search-path-prefix as it may not exist
        if not secret_manager_name and subscription_id != "azure" and subscription_id != "all":
            # Only add search-path-prefix for non-Secret Manager runs (local development)
            connection_name = f"azure_{formatted_subscription_id}"
            base_command.extend(["--search-path-prefix", connection_name])
        
        log(f"Executing command: {' '.join(base_command)}")
        job_manager.update_job_status(job_id, 'running', progress=30)

        # Use retry mechanism for database connection issues
        try:
            from shared.steampipe_health import run_powerpipe_with_retry
            result = run_powerpipe_with_retry(base_command, max_retries=3, retry_delay=15)
            stdout = result.stdout
            stderr = result.stderr
            process_returncode = result.returncode
        except Exception as e:
            log(f"Failed to execute command with retry: {e}")
            # Fallback to original method
            process = subprocess.Popen(
                base_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                text=True,
                cwd="/app/steampipe-mod-azure-compliance"
            )
            stdout, stderr = process.communicate()
            process_returncode = process.returncode

        job_manager.update_job_status(job_id, 'running', progress=80)

        # Try to find and read the exported JSON file
        json_file_path = None
        json_data = None
        
        for line in stdout.splitlines():
            if "File exported to" in line:
                json_file_path = line.split()[-1].strip()
                break
                
        if json_file_path and os.path.exists(json_file_path):
            try:
                with open(json_file_path, 'r') as f:
                    json_data = json.load(f)
                    
                # Save the result
                job_manager.save_result(job_id, 'azure', command_name, subscription_id, json_data)
                log(f"Job {job_id} completed successfully")
                
            except Exception as e:
                error_msg = f"Error reading JSON output: {str(e)}"
                log(error_msg)
                job_manager.update_job_status(job_id, 'failed', error=error_msg)
        else:
            # If no JSON file, save the raw output
            result = {
                'status': 'completed' if process_returncode == 0 else 'error',
                'stdout': stdout,
                'stderr': stderr,
                'return_code': process_returncode
            }

            if process_returncode == 0:
                job_manager.save_result(job_id, 'azure', command_name, subscription_id, result)
            else:
                job_manager.update_job_status(job_id, 'failed', error=stderr or stdout)
                
    except Exception as e:
        error_msg = f"Error executing benchmark: {str(e)}"
        log(error_msg)
        job_manager.update_job_status(job_id, 'failed', error=error_msg)

def run_powerpipe_command(command_name, subscription_id, secret_manager_name=None):
    """Execute Powerpipe benchmark command with subscription_id and return real-time output."""
    try:
        # If secret_manager_name is provided, reinitialize credentials
        if secret_manager_name:
            creds, error = get_service_credentials(secret_manager_name)
            if creds is None:  # Error occurred
                return {
                    "status": "error",
                    "error": f"Failed to get credentials from Secret Manager: {error}",
                    "command": command_name
                }
        # Replace hyphens with underscores in subscription_id for path formatting
        formatted_subscription_id = subscription_id.replace("-", "_")
        
        # Construct the command based on whether we want to scan all subscriptions or a specific one
        base_command = [
            "powerpipe",
            "benchmark",
            "run",
            command_name,
            "--var",
            f"subscription_id={subscription_id}",
            "--export=json"
        ]
        
        # Only add search-path-prefix if we have a specific subscription connection
        if subscription_id != "azure" and subscription_id != "all":
            # Check if specific connection exists
            connection_name = f"azure_{formatted_subscription_id}"
            base_command.extend(["--search-path-prefix", connection_name])
        
        log(f"Executing command: {' '.join(base_command)}")
        
        # Create a process with pipe to capture output
        process = subprocess.Popen(
            base_command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            text=True,
            cwd="/app/steampipe-mod-azure-compliance"
        )
        
        # Read output and error streams
        stdout, stderr = process.communicate()

        # Try to find and read the exported JSON file
        json_file_path = None
        json_data = None
        
        for line in stdout.splitlines():
            if "File exported to" in line:
                json_file_path = line.split()[-1].strip()
                break
                
        if json_file_path and os.path.exists(json_file_path):
            try:
                with open(json_file_path, 'r') as f:
                    json_data = json.load(f)
            except Exception as e:
                log(f"Error reading JSON file: {str(e)}")

        # Save consolidated results to our JSON file
        # Only include the actual benchmark results, not the raw stdout which may contain invalid characters
        result_data = {
            "benchmark_results": json_data,
            "command": command_name,
            "error": stderr if stderr else None
        }
        
        output_file = save_output_to_json(subscription_id, command_name, result_data)

        return {
            "status": "success" if process.returncode == 0 else "error",
            "output": stdout,
            "error": stderr if stderr else None,
            "command": command_name,
            "return_code": process.returncode,
            "output_file": output_file,
            "benchmark_results": json_data
        }
    except Exception as e:
        log(f"Error executing command: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "command": command_name
        }

def extract_steampipe_data(response_text: str, control_name_suggestion: str = "testcontrol") -> dict:
    """Extracts and formats the Steampipe control and query."""
    try:
        control_block = ""
        sql_query_block = ""
        control_name = control_name_suggestion

        # Extract the entire control block
        control_match = re.search(r'control\s+"([^"]+)"\s*\{[^}]*\}', response_text, re.DOTALL | re.IGNORECASE)
        if control_match:
            control_name = control_match.group(1).strip()
            control_block = control_match.group(0).strip()

        # Extract the entire query block
        query_match = re.search(r'query\s+"[^"]+"\s*\{[^}]*\}', response_text, re.DOTALL | re.IGNORECASE)
        if not query_match:  # If not in query block, look for a plain SQL block
           query_match = re.search(r'`sql(.*?)`', response_text, re.DOTALL | re.IGNORECASE)
           if query_match: # found sql block only
                sql_query_content = query_match.group(1).strip()
                # create query block
                sql_query_block = f'''query "{control_name}" {{
  sql = <<-EOQ
{sql_query_content}
  EOQ
}}'''
        else:
            sql_query_block = query_match.group(0).strip()

        # Construct default control block, if control block missing, but, sql block available
        if not control_block and sql_query_block:
            control_block = f"""control "{control_name}" {{
  title       = "Generated Control for {control_name}"
  description = "Generated control based on user prompt."
  query       = query.{control_name}
  tags = merge(local.azure_compliance_common_tags, {{
    service = "Azure/Generated"  // Modify as needed
  }})
}}"""

        if not control_block or not sql_query_block: #check if we have both blocks
            return {"error": "Could not find both control and query blocks."}

        return {
            "control_block": control_block,
            "query_block": sql_query_block,
            "control_name": control_name,
        }

    except Exception as e:
        return {"error": f"Error during extraction/formatting: {e}"}

def write_steampipe_control_file(filepath: str, extracted_data: dict) -> None:
    """Overwrites the Steampipe control file with the new content."""
    control_name = extracted_data.get('control_name', 'testcontrol')  # Fallback
    control_block = extracted_data.get('control_block')
    query_block = extracted_data.get('query_block')

    if not control_block or not query_block:
        raise ValueError("Control block and query block are required.")

    try:
        # Construct the complete file content
        new_file_content = f"""locals {{
  my_controls_common_tags = merge(local.azure_compliance_common_tags, {{
    type = "Benchmark"
  }})
}}

benchmark "my_controls" {{
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.{control_name},
  ]
  tags = local.my_controls_common_tags
}}

{control_block}

{query_block}
"""
        # Write the new content to the file (overwrites existing content)
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(new_file_content)

    except Exception as e:
        raise Exception(f"Error writing to Steampipe control file: {e}")

# API Endpoints
def check_subscription():
    """
    Decorator to verify active subscription status without charging credits.
    Similar to charge_credits but uses the check_subscription endpoint instead.
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if AUTH_TYPE == 'adc':
                log("Local development detected, skipping subscription verification", 
                    auth_type=AUTH_TYPE,
                    severity="INFO")
                return f(*args, **kwargs)
            try:
                # Get IAP token from headers
                iap_token = request.headers.get('X-Original-Iap-Jwt')
                log("Processing request headers", 
                    headers=dict(request.headers),
                    severity="DEBUG")
                
                host = request.headers.get('Referer')
                if not iap_token:
                    # For now, allow access without IAP token
                    log("No IAP token provided, allowing access (IAP not enforced)", severity="INFO")
                    return f(*args, **kwargs)

                # Make request to check_subscription endpoint on neosec marketplace
                headers = {
                    "token": iap_token,
                    "Content-Type": "application/json",
                    "subdomain": host
                }
                
                log("Making request to check subscription", 
                    url=NEOSEC_SUBSCRIPTION_URL,  # You'll need to define this constant
                    severity="DEBUG")
                    
                # No need to include cost_credits for subscription check
                data = {}
                
                response = requests.post(
                    NEOSEC_SUBSCRIPTION_URL,
                    headers=headers,
                    json=data
                )

                log("Response from subscription check", 
                    response_status=response.status_code,
                    response_body=response.text,
                    severity="DEBUG")
                
                if response.status_code != 200:
                    log("Subscription verification failed", 
                        status_code=response.status_code,
                        severity="ERROR")
                    return jsonify({
                        "status": "error",
                        "error": "No active subscription found"
                    }), 402  # Payment Required

                return f(*args, **kwargs)
            except Exception as e:
                log("Subscription verification error",
                    error=str(e),
                    stack_trace=traceback.format_exc(),
                    severity="ERROR")
                # For now, allow access on error
                log("Allowing access despite subscription check error", severity="INFO")
                return f(*args, **kwargs)
        return decorated_function
    return decorator

def charge_credits(cost_credits=1):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if AUTH_TYPE == 'adc':
                log("Local development detected, skipping IAP verification", 
                    auth_type=AUTH_TYPE,
                    severity="INFO")
                return f(*args, **kwargs)
            try:
                # Get IAP token from headers
                iap_token = request.headers.get('X-Original-Iap-Jwt')
                log("Processing request headers", 
                    headers=dict(request.headers),
                    severity="DEBUG")
                
                host = request.headers.get('Referer')
                if not iap_token:
                    # For now, allow access without IAP token
                    log("No IAP token provided, allowing access (IAP not enforced)", severity="INFO")
                    return f(*args, **kwargs)

                # Make request to charge_credit endpoint on neosec marketplace
                headers = {
                    "token": iap_token,
                    "Content-Type": "application/json",
                    "subdomain": host
                }
                
                log("Making request to Neosec marketplace", 
                    url=NEOSEC_VERIFICATION_URL,
                    credits=cost_credits,
                    severity="DEBUG")
                    
                data = {
                    "cost_credits": cost_credits
                }
                
                response = requests.post(
                    NEOSEC_VERIFICATION_URL,
                    headers=headers,
                    json=data
                )

                log("Response from charge credit", 
                    response_status=response.status_code,
                    response_body=response.text,
                    severity="DEBUG")
                
                if response.status_code != 200:
                    log("Failed to charge credit", 
                        status_code=response.status_code,
                        severity="ERROR")
                    return jsonify({
                        "status": "error",
                        "error": "Failed to charge credit"
                    }), 400

                return f(*args, **kwargs)
            except Exception as e:
                log("IAP verification error",
                    error=str(e),
                    stack_trace=traceback.format_exc(),
                    severity="ERROR")
                # For now, allow access on error
                log("Allowing access despite IAP check error", severity="INFO")
                return f(*args, **kwargs)
        return decorated_function
    return decorator


@app.route('/api/azure/generate', methods=['POST'])
def generate():
    """API endpoint to generate control."""
    try:
        data = request.get_json()
        user_query = data['prompt']  # Get the prompt from the request

        response_text = run_prompt(user_query)
        extracted_data = extract_steampipe_data(response_text)

        if "error" in extracted_data:
            return jsonify({'error': extracted_data["error"], 'response': response_text}), 500

        write_steampipe_control_file(CONTROLS_FILE_PATH, extracted_data)

        # Return the combined, formatted output to the user.
        formatted_output = f"{extracted_data['control_block']}\n\n{extracted_data['query_block']}"
        return jsonify({'response': formatted_output})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/azure/run-azure-benchmark', methods=['POST'])
@check_subscription()
def run_benchmark():
    """API endpoint to run Powerpipe benchmark."""
    try:
        data = request.get_json()
        if not data or 'benchmark' not in data:
            return jsonify({
                "status": "error",
                "error": "Missing 'benchmark' parameter"
            }), 400

        if 'subscription_id' not in data:
            return jsonify({
                "status": "error",
                "error": "Missing 'subscription_id' parameter"
            }), 400

        benchmark = data['benchmark']
        subscription_id = data['subscription_id']
        secret_manager_name = data.get('secret_manager_name')  # Optional parameter for deployment

        log(f"Running benchmark: {benchmark} for subscription: {subscription_id}")
        if secret_manager_name:
            log(f"Using Secret Manager authentication with secret: {secret_manager_name}")
        else:
            log("Using Azure Default Credentials")

        # Run the Powerpipe command with subscription_id and secret_manager_name
        result = run_powerpipe_command(benchmark, subscription_id, secret_manager_name)
        return jsonify(result)
        
    except Exception as e:
        log(f"API error: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/api/azure/run-azure-benchmark-async', methods=['POST'])
@check_subscription()
def run_benchmark_async():
    """API endpoint to run powerpipe benchmark asynchronously"""
    try:
        data = request.get_json()
        if not data or 'benchmark' not in data:
            return jsonify({
                "status": "error",
                "error": "Missing 'benchmark' parameter"
            }), 400

        if 'subscription_id' not in data:
            return jsonify({
                "status": "error",
                "error": "Missing 'subscription_id' parameter"
            }), 400

        # Ensure Steampipe service is running before starting benchmark
        log("Ensuring Steampipe service is ready before benchmark execution...")
        try:
            # Set service type environment variable to avoid plugin conflicts
            os.environ['SERVICE_TYPE'] = 'azure'
            from shared.steampipe_health import ensure_steampipe_running
            ensure_steampipe_running()
            log("Steampipe service verified as ready")
        except ImportError as e:
            log(f"Failed to import steampipe_health module: {e}", severity="ERROR")
            return jsonify({
                "status": "error",
                "error": f"Steampipe health module import failed: {str(e)}"
            }), 503
        except Exception as e:
            log(f"Failed to ensure Steampipe service is ready: {e}", severity="ERROR")
            return jsonify({
                "status": "error",
                "error": "Steampipe service is not available"
            }), 503

        benchmark = data['benchmark']
        subscription_id = data['subscription_id']
        secret_manager_name = data.get('secret_manager_name')  # Optional parameter for deployment

        log(f"Running benchmark: {benchmark} for subscription: {subscription_id}")
        if secret_manager_name:
            log(f"Using Secret Manager authentication with secret: {secret_manager_name}")
        else:
            log("Using Azure Default Credentials (local development)")

        # Create a job
        job_id = job_manager.create_job('azure', benchmark, subscription_id)

        # Run the benchmark in a background thread
        thread = threading.Thread(
            target=run_powerpipe_command_async,
            args=(job_id, benchmark, subscription_id, secret_manager_name),
            daemon=True
        )
        thread.start()

        log(f"Started async benchmark job: {job_id}")

        return jsonify({
            "status": "success",
            "job_id": job_id,
            "message": "Benchmark job started",
            "check_status_url": f"/api/azure/job/{job_id}/status",
            "get_result_url": f"/api/azure/job/{job_id}/result"
        })

    except Exception as e:
        log(f"API error: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/api/azure/job/<job_id>/status', methods=['GET'])
def get_job_status(job_id):
    """Get the status of a running job"""
    job_status = job_manager.get_job_status(job_id)
    
    if not job_status:
        return jsonify({
            "status": "error",
            "error": "Job not found"
        }), 404
    
    return jsonify(job_status)

@app.route('/api/azure/job/<job_id>/result', methods=['GET'])
def get_job_result(job_id):
    """Get the result of a completed job"""
    job_status = job_manager.get_job_status(job_id)
    
    if not job_status:
        return jsonify({
            "status": "error",
            "error": "Job not found"
        }), 404
    
    if job_status['status'] != 'completed':
        return jsonify({
            "status": "error",
            "error": f"Job is {job_status['status']}, not completed",
            "job_status": job_status
        }), 400
    
    result = job_manager.get_result_by_job_id(job_id)
    if result:
        return jsonify(result)
    else:
        return jsonify({
            "status": "error",
            "error": "Result not found"
        }), 404

@app.route('/api/azure/latest-result/<benchmark>/<subscription_id>', methods=['GET'])
def get_latest_result(benchmark, subscription_id):
    """Get the latest result for a specific benchmark and subscription"""
    result = job_manager.get_latest_result('azure', benchmark, subscription_id)
    
    if result:
        return jsonify(result)
    else:
        return jsonify({
            "status": "error",
            "error": "No results found for this benchmark and subscription"
        }), 404

@app.route('/api/azure/list-results', methods=['GET'])
def list_results():
    """List all available results"""
    results = job_manager.list_available_results('azure')
    return jsonify({
        "status": "success",
        "results": results
    })

@app.route('/api/azure/jobs', methods=['GET'])
def list_jobs():
    """List all jobs"""
    jobs = job_manager.get_all_jobs('azure')
    return jsonify({
        "status": "success",
        "jobs": jobs
    })

@app.route('/api/azure/benchmark', methods=['POST'])
@check_subscription()
def run_benchmark_alias():
    """Alias endpoint for run-azure-benchmark-async"""
    return run_benchmark_async()

@app.route('/api/azure/reports', methods=['GET'])
def list_reports():
    """List all reports for a customer"""
    try:
        # List all saved report files in the outputs directory
        output_dir = "/app/steampipe-mod-azure-compliance/outputs"
        reports = []
        
        if os.path.exists(output_dir):
            for filename in os.listdir(output_dir):
                if filename.endswith('.json'):
                    # Parse filename to extract details
                    parts = filename.replace('.json', '').split('_')
                    if len(parts) >= 3:
                        subscription_id = parts[0]
                        benchmark = '_'.join(parts[1:-2])  # Handle benchmarks with underscores
                        timestamp = parts[-2] + '_' + parts[-1]
                        
                        # Get file size and modification time
                        file_path = os.path.join(output_dir, filename)
                        file_stat = os.stat(file_path)
                        
                        reports.append({
                            "filename": filename,
                            "subscription_id": subscription_id,
                            "benchmark": benchmark,
                            "timestamp": timestamp,
                            "size": file_stat.st_size,
                            "modified": datetime.fromtimestamp(file_stat.st_mtime).isoformat()
                        })
        
        # Sort by modification time, most recent first
        reports.sort(key=lambda x: x['modified'], reverse=True)
        
        return jsonify({
            "status": "success",
            "reports": reports,
            "total": len(reports)
        })
    except Exception as e:
        log(f"Error listing reports: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/api/azure/listReports/<subscription_id>', methods=['GET'])
def list_reports_by_subscription(subscription_id):
    """List saved report files for a specific Azure subscription"""
    try:
        # List all saved report files for the specified subscription
        output_dir = "/app/steampipe-mod-azure-compliance/outputs"
        reports = []
        
        if os.path.exists(output_dir):
            for filename in os.listdir(output_dir):
                if filename.startswith(f"{subscription_id}_") and filename.endswith('.json'):
                    # Parse filename to extract details
                    parts = filename.replace('.json', '').split('_')
                    if len(parts) >= 3:
                        benchmark = '_'.join(parts[1:-2])  # Handle benchmarks with underscores
                        timestamp = parts[-2] + '_' + parts[-1]
                        
                        # Get file size and modification time
                        file_path = os.path.join(output_dir, filename)
                        file_stat = os.stat(file_path)
                        
                        reports.append({
                            "filename": filename,
                            "subscription_id": subscription_id,
                            "benchmark": benchmark,
                            "timestamp": timestamp,
                            "size": file_stat.st_size,
                            "modified": datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                            "path": file_path
                        })
        
        # Sort by modification time, most recent first
        reports.sort(key=lambda x: x['modified'], reverse=True)
        
        return jsonify({
            "status": "success",
            "subscription_id": subscription_id,
            "reports": reports,
            "total": len(reports)
        })
    except Exception as e:
        log(f"Error listing reports for subscription {subscription_id}: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/api/azure/get-tenant', methods=['GET'])
def get_tenant():
    """API endpoint to fetch Azure AD tenant information."""
    try:
        tenant_id, error = get_azure_tenant_id()
            
        if error:
            return jsonify({"status": "error", "error": error}), 500
        
        return jsonify({
            "status": "success",
            "tenant": tenant_id
        })
    except Exception as e:
        log(f"Error in get-tenant API: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/api/azure/get-subscriptions', methods=['POST'])
def get_subscriptions():
    """API endpoint to fetch Azure subscriptions, optionally filtered by tenant ID."""
    try:
        # Get tenant_id from the request body (JSON payload)
        data = request.json
        tenant_id = data.get('tenant_id', None)  # Use .get() to safely access tenant_id

        # Fetch subscriptions
        subscriptions = get_azure_subscriptions(credentials, tenant_id)

        # Add tenant_id to each subscription
        for sub in subscriptions:
            sub["tenant_id"] = tenant_id

        return jsonify({
            "status": "success",
            "subscriptions": subscriptions,
            "tenant_id": tenant_id
        })
    except Exception as e:
        log(f"Error in get-subscriptions API: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Basic health check endpoint"""
    return jsonify({
        "status": "running",
        "service": "azure-compliance"
    })

def main():
    """Main application entry point"""
    global credentials, subscription_id, AUTH_TYPE

    log("Starting application initialization...")

    # Set service type environment variable early
    os.environ['SERVICE_TYPE'] = 'azure'

    # Initialize credentials first
    log("Initializing credentials...")
    credentials, error = get_service_credentials()

    if credentials is None:
        log(f"CRITICAL: Failed to initialize credentials: {error}")
        sys.exit(1)

    log(f"Credentials initialized. Subscription ID: {subscription_id}")

    # Get tenant ID
    tenant_id = None
    try:
        log("Attempting to get tenant ID...")
        tenant_id, error = get_azure_tenant_id()
        if tenant_id:
            log(f"Using tenant ID: {tenant_id}")
        elif error:
            log(f"Could not determine tenant ID: {error}")
    except Exception as e:
        log(f"Error getting tenant ID: {str(e)}")

    # Generate Steampipe configuration with credentials
    log("Generating Steampipe configuration...")
    is_cloud_run = os.getenv('K_SERVICE') is not None
    config_path = run_steampipe.generate_and_save_config(
        credentials=credentials, 
        is_cloud_run=is_cloud_run,
        tenant_id=tenant_id
    )
    
    # Start Steampipe service with three attempts
    if not init_steampipe_service():
       log("CRITICAL: Failed to start Steampipe service after 3 attempts. Exiting.")
       sys.exit(1)
    
    # Start Flask application
    port = int(os.getenv('PORT', 8083))
    log(f"Starting Flask application on port {port}...")
    app.run(host='0.0.0.0', port=port, debug=False)

if __name__ == '__main__':
    main()