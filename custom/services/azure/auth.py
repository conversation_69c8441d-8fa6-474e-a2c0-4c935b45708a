from flask import Blueprint, request, jsonify, abort
from azure.identity import DefaultAzureCredential, ClientSecretCredential
from google.auth import default
from google.oauth2 import service_account
from google.cloud import secretmanager
from functools import wraps
import os
import sys
import json
from google.auth.credentials import Credentials
import logging
import subprocess
import traceback
sys.path.append('/app/shared')
try:
    from secret_manager import SecretManagerClient, validate_credentials
except ImportError:
    # Fallback for local development
    SecretManagerClient = None
    validate_credentials = None

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create a blueprint
auth = Blueprint('auth', __name__)

# Use a writable directory for authentication files
AUTHENTICATION_FOLDER = os.path.join('/tmp', 'authentication')
SERVICE_ACCOUNT_FILE = os.path.join(AUTHENTICATION_FOLDER, 'azure-service-account.json')

def get_service_credentials(secret_manager_name=None):
    try:
        # Get authentication type from environment variables
        auth_type = os.getenv('AUTH_TYPE', 'adc').lower()  # Default to ADC for local development
        logger.info(f"Using authentication type: {auth_type}")

        # If secret_manager_name is provided, use Secret Manager authentication
        if secret_manager_name:
            logger.info(f"Using Secret Manager with secret name: {secret_manager_name}")
            creds, error = get_credentials_from_secret_manager(secret_manager_name)
            if creds:
                return creds, error
            else:
                return None, error
        elif auth_type == 'adc':
            try:
                logger.info("Attempting to use Azure Default Credentials")
                credentials = DefaultAzureCredential()
                logger.info(f"Credential type: {type(credentials).__name__}")
                return credentials, None
            except Exception as e:
                logger.error(f"ADC authentication failed: {str(e)}")
                logger.error(f"Detailed error: {traceback.format_exc()}")
                return None, f"ADC authentication failed: {str(e)}"
        
        else:
            # If no secret manager name provided, fall back to default ADC
            logger.info("No secret manager name provided, using Azure Default Credentials")
            try:
                credentials = DefaultAzureCredential()
                logger.info(f"Credential type: {type(credentials).__name__}")
                return credentials, None
            except Exception as e:
                logger.error(f"ADC authentication failed: {str(e)}")
                logger.error(f"Detailed error: {traceback.format_exc()}")
                return None, f"ADC authentication failed: {str(e)}"
                

    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        logger.error(f"Detailed error: {traceback.format_exc()}")
        return None, f"Authentication error: {str(e)}"

def get_credentials_from_secret_manager(secret_name):
    """Get Azure credentials from Secret Manager using secret name"""
    try:
        # Get default credentials to access Secret Manager
        default_credentials, default_project_id = default()

        # Initialize Secret Manager client
        secret_client = secretmanager.SecretManagerServiceClient(credentials=default_credentials)

        # Get project ID from environment or use default
        project_id = os.getenv('PROJECT_ID') or default_project_id
        if not project_id:
            error_msg = "No project ID available for Secret Manager access"
            logger.error(error_msg)
            return None, error_msg

        # Construct the secret path
        name = f"projects/{project_id}/secrets/{secret_name}/versions/latest"

        try:
            response = secret_client.access_secret_version(request={"name": name})
            secret_content = response.payload.data.decode("UTF-8")
            logger.info(f"Successfully retrieved secret: {secret_name}")
        except Exception as e:
            logger.error(f"Failed to access secret: {str(e)}")
            logger.error(f"Secret path attempted: {name}")
            logger.error(f"Detailed error: {traceback.format_exc()}")
            return None, f"Failed to access secret: {str(e)}"

        try:
            service_account_json = json.loads(secret_content)
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in secret content: {str(e)}")
            return None, f"Invalid JSON in secret content: {str(e)}"

        # Extract Azure service principal credentials
        client_id = service_account_json.get('client_id')
        client_secret = service_account_json.get('client_secret')
        tenant_id = service_account_json.get('tenant_id')

        if not all([client_id, client_secret, tenant_id]):
            error_msg = "Required fields (client_id, client_secret, tenant_id) not found in service account JSON"
            logger.error(error_msg)
            return None, error_msg

        try:
            # Create Azure client credentials
            credentials = ClientSecretCredential(
                tenant_id=tenant_id,
                client_id=client_id,
                client_secret=client_secret
            )

            logger.info(f"Successfully loaded Azure credentials from secret: {secret_name}")
            logger.info(f"Credential type: ClientSecretCredential")

            # Store these values in environment variables for other components
            os.environ['AZURE_TENANT_ID'] = tenant_id
            os.environ['AZURE_CLIENT_ID'] = client_id
            os.environ['AZURE_CLIENT_SECRET'] = client_secret

            return credentials, None

        except Exception as e:
            logger.error(f"Failed to create credentials from service account file: {str(e)}")
            logger.error(f"Detailed error: {traceback.format_exc()}")
            return None, f"Failed to create credentials from service account file: {str(e)}"

    except Exception as e:
        logger.error(f"Error with Secret Manager authentication: {str(e)}")
        logger.error(f"Detailed error: {traceback.format_exc()}")
        return None, f"Secret Manager authentication failed: {str(e)}"

def get_customer_credentials_from_secret(customer_id):
    """Get customer-specific Azure credentials from Secret Manager"""
    try:
        # Get project ID
        project_id = os.getenv('GCP_PROJECT_ID')
        if not project_id:
            credentials, project_id = default()
        
        # Initialize Secret Manager client
        sm_client = SecretManagerClient(project_id=project_id)
        
        # Get customer credentials
        creds, error = sm_client.get_customer_credentials(customer_id, 'azure')
        if error:
            logger.error(f"Error getting customer credentials: {error}")
            return None, error
        
        # Validate credentials
        if validate_credentials:
            valid, validation_error = validate_credentials('azure', creds)
            if not valid:
                logger.error(f"Invalid credentials: {validation_error}")
                return None, validation_error
        
        # Clear any Azure-specific environment variables that could interfere
        azure_env_vars = [
            'AZURE_SUBSCRIPTION_ID', 'AZURE_TENANT_ID', 
            'AZURE_CLIENT_ID', 'AZURE_CLIENT_SECRET',
            'AZURE_CREDENTIAL_SCOPE'
        ]
        
        for env_var in azure_env_vars:
            if env_var in os.environ:
                del os.environ[env_var]
        
        # Extract Azure service principal credentials
        client_id = creds['client_id']
        client_secret = creds['client_secret']
        tenant_id = creds['tenant_id']
        
        # Create Azure client credentials
        azure_credentials = ClientSecretCredential(
            tenant_id=tenant_id,
            client_id=client_id,
            client_secret=client_secret
        )
        
        # Store these values in environment variables for other components
        os.environ['AZURE_TENANT_ID'] = tenant_id
        os.environ['AZURE_CLIENT_ID'] = client_id
        os.environ['AZURE_CLIENT_SECRET'] = client_secret
        
        # Also store subscription_id if provided
        if 'subscription_id' in creds:
            os.environ['AZURE_SUBSCRIPTION_ID'] = creds['subscription_id']
        
        logger.info(f"Successfully loaded Azure credentials for customer {customer_id}")
        return azure_credentials
        
    except Exception as e:
        logger.error(f"Error with Secret Manager authentication: {str(e)}")
        logger.error(f"Detailed error: {traceback.format_exc()}")
        return None, f"Secret Manager authentication failed: {str(e)}"
