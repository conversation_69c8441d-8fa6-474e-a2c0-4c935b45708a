# Azure Compliance Service
ARG BASE_IMAGE=steampipe-base:latest
FROM ${BASE_IMAGE}

USER root

# Install Azure CLI
RUN curl -sL https://aka.ms/InstallAzureCLIDeb | bash

# Copy shared custom code
COPY --chown=steampipe:steampipe custom/shared /app/shared

# Install Python dependencies
COPY --chown=steampipe:steampipe custom/services/azure/requirements.txt /app/requirements.txt
RUN pip3 install --no-cache-dir -r /app/requirements.txt

# Copy the Azure compliance mod
COPY --chown=steampipe:steampipe steampipe-mod-azure-compliance /app/steampipe-mod-azure-compliance

# Apply any custom modifications (if they exist)

# Copy custom Flask application and config
COPY --chown=steampipe:steampipe custom/services/azure/*.py /app/
COPY --chown=steampipe:steampipe custom/services/azure/steampipe-config /app/steampipe-config

USER steampipe

# Skip mod install for now - will be done at runtime if needed

ENV FLASK_APP=app.py
ENV SERVICE_TYPE=azure

EXPOSE 8080

CMD ["python3", "-u", "/app/app.py"]