steps:
  - name: gcr.io/cloud-builders/docker
    id: qemu
    args:
      - run
      - '--privileged'
      - multiarch/qemu-user-static
      - '--reset'
      - '-p'
      - 'yes'

  - name: gcr.io/cloud-builders/docker
    id: build
    env:
      - DOCKER_BUILDKIT=1
      - DOCKER_CLI_EXPERIMENTAL=enabled
    args:
      - buildx
      - create
      - '--use'
      - '--name=mybuilder'
      - '--platform=linux/amd64'

  - name: gcr.io/cloud-builders/docker
    env:
      - DOCKER_BUILDKIT=1
      - DOCKER_CLI_EXPERIMENTAL=enabled
    args:
      - buildx
      - build
      - '--platform=linux/amd64'
      - '--build-arg'
      - STEAMPIPE_DATABASE_PASSWORD=steampipe
      - '--build-arg'
      - STEAMPIPE_DATABASE_USER=steampipe
      - '--build-arg'
      - STEAMPIPE_DATABASE_NAME=steampipe
      - '--build-arg'
      - STEAMPIPE_INIT_TIMEOUT=600
      - '--build-arg'
      - STEAMPIPE_TIMEOUT=900s
      - '--build-arg'
      - CUSTOMER_ID=local
      - '--build-arg'
      - PROJECT_ID=vratant-test-prj
      - '--build-arg'
      - AUTH_TYPE=sa
      - '--tag'
      - gcr.io/$PROJECT_ID/steampipe-service-gcp
      - '--load'
      - '--provenance=false'
      - '--no-cache'
      - '--progress=plain'
      - .
  - name: gcr.io/cloud-builders/docker
    args:
      - push
      - gcr.io/$PROJECT_ID/steampipe-service-gcp

  - name: gcr.io/cloud-builders/gcloud
    args:
      - run
      - deploy
      - steampipe-service-gcp
      - '--image'
      - gcr.io/$PROJECT_ID/steampipe-service-gcp
      - '--region'
      - us-central1
      - '--platform'
      - managed
      - '--allow-unauthenticated'
      - '--memory'
      - '8Gi'
      - '--cpu'
      - '4'
      - '--port'
      - '8080'
      - '--set-env-vars'
      - >-
        STEAMPIPE_UPDATE_CHECK=false,
        POWERPIPE_UPDATE_CHECK=false,
        STEAMPIPE_DATABASE_PASSWORD=steampipe,
        STEAMPIPE_DATABASE_USER=steampipe,
        STEAMPIPE_DATABASE_NAME=steampipe,
        STEAMPIPE_INIT_TIMEOUT=600,
        STEAMPIPE_TIMEOUT=900s,
        CUSTOMER_ID=local,
        PROJECT_ID=vratant-test-prj,
        AUTH_TYPE=sa
      - '--min-instances'
      - '1'
      - '--timeout'
      - '900s'
      - '--no-cpu-throttling'
      - '--max-instances'
      - '10'

options:
  machineType: E2_HIGHCPU_8
  logging: CLOUD_LOGGING_ONLY
  env:
    - DOCKER_CLI_EXPERIMENTAL=enabled
    - DOCKER_BUILDKIT=1
  dynamicSubstitutions: true