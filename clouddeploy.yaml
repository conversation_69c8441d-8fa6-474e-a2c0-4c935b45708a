# Cloud Build Configuration for Steampipe Compliance Services
# Builds and deploys the three main compliance containers to Cloud Run: GCP, AWS, and Azure

steps:
  # Step 1: Setup multi-platform build environment
  - name: gcr.io/cloud-builders/docker
    id: qemu
    args:
      - run
      - '--privileged'
      - multiarch/qemu-user-static
      - '--reset'
      - '-p'
      - 'yes'

  - name: gcr.io/cloud-builders/docker
    id: buildx-setup
    env:
      - DOCKER_BUILDKIT=1
      - DOCKER_CLI_EXPERIMENTAL=enabled
    args:
      - buildx
      - create
      - '--use'
      - '--name=mybuilder'
      - '--platform=linux/amd64'

  # Step 2: Build base image first (required by compliance services)
  - name: gcr.io/cloud-builders/docker
    id: build-base
    env:
      - DOCKER_BUILDKIT=1
      - DOCKER_CLI_EXPERIMENTAL=enabled
    args:
      - buildx
      - build
      - '--platform=linux/amd64'
      - '--tag'
      - gcr.io/$PROJECT_ID/steampipe-base:latest
      - '--load'
      - '--provenance=false'
      - '--no-cache'
      - '--progress=plain'
      - '-f'
      - Base_image_steampipe/Dockerfile
      - .
    waitFor: ['buildx-setup']

  - name: gcr.io/cloud-builders/docker
    id: push-base
    args:
      - push
      - gcr.io/$PROJECT_ID/steampipe-base:latest
    waitFor: ['build-base']

  # Step 3: Build GCP Compliance Service
  - name: gcr.io/cloud-builders/docker
    id: build-gcp
    env:
      - DOCKER_BUILDKIT=1
      - DOCKER_CLI_EXPERIMENTAL=enabled
    args:
      - buildx
      - build
      - '--platform=linux/amd64'
      - '--build-arg'
      - BASE_IMAGE=gcr.io/$PROJECT_ID/steampipe-base:latest
      - '--build-arg'
      - AUTH_TYPE=secret_manager
      - '--build-arg'
      - PROJECT_ID=$PROJECT_ID
      - '--tag'
      - gcr.io/$PROJECT_ID/steampipe-gcp-compliance
      - '--load'
      - '--provenance=false'
      - '--no-cache'
      - '--progress=plain'
      - '-f'
      - custom/services/gcp/Dockerfile
      - .
    waitFor: ['push-base']

  - name: gcr.io/cloud-builders/docker
    id: push-gcp
    args:
      - push
      - gcr.io/$PROJECT_ID/steampipe-gcp-compliance
    waitFor: ['build-gcp']

  # Step 4: Build AWS Compliance Service
  - name: gcr.io/cloud-builders/docker
    id: build-aws
    env:
      - DOCKER_BUILDKIT=1
      - DOCKER_CLI_EXPERIMENTAL=enabled
    args:
      - buildx
      - build
      - '--platform=linux/amd64'
      - '--build-arg'
      - BASE_IMAGE=gcr.io/$PROJECT_ID/steampipe-base:latest
      - '--build-arg'
      - AUTH_TYPE=secret_manager
      - '--build-arg'
      - PROJECT_ID=$PROJECT_ID
      - '--tag'
      - gcr.io/$PROJECT_ID/steampipe-aws-compliance
      - '--load'
      - '--provenance=false'
      - '--no-cache'
      - '--progress=plain'
      - '-f'
      - custom/services/aws/Dockerfile
      - .
    waitFor: ['push-base']

  - name: gcr.io/cloud-builders/docker
    id: push-aws
    args:
      - push
      - gcr.io/$PROJECT_ID/steampipe-aws-compliance
    waitFor: ['build-aws']

  # Step 5: Build Azure Compliance Service
  - name: gcr.io/cloud-builders/docker
    id: build-azure
    env:
      - DOCKER_BUILDKIT=1
      - DOCKER_CLI_EXPERIMENTAL=enabled
    args:
      - buildx
      - build
      - '--platform=linux/amd64'
      - '--build-arg'
      - BASE_IMAGE=gcr.io/$PROJECT_ID/steampipe-base:latest
      - '--build-arg'
      - AUTH_TYPE=secret_manager
      - '--build-arg'
      - PROJECT_ID=$PROJECT_ID
      - '--tag'
      - gcr.io/$PROJECT_ID/steampipe-azure-compliance
      - '--load'
      - '--provenance=false'
      - '--no-cache'
      - '--progress=plain'
      - '-f'
      - custom/services/azure/Dockerfile
      - .
    waitFor: ['push-base']

  - name: gcr.io/cloud-builders/docker
    id: push-azure
    args:
      - push
      - gcr.io/$PROJECT_ID/steampipe-azure-compliance
    waitFor: ['build-azure']

  # Step 6: Deploy GCP Compliance Service to Cloud Run
  - name: gcr.io/cloud-builders/gcloud
    id: deploy-gcp
    args:
      - run
      - deploy
      - steampipe-gcp-compliance
      - '--image'
      - gcr.io/$PROJECT_ID/steampipe-gcp-compliance
      - '--region'
      - us-central1
      - '--platform'
      - managed
      - '--allow-unauthenticated'
      - '--memory'
      - '8Gi'
      - '--cpu'
      - '4'
      - '--port'
      - '8080'
      - '--set-env-vars'
      - >-
        AUTH_TYPE=secret_manager,
        PROJECT_ID=$PROJECT_ID,
        STEAMPIPE_UPDATE_CHECK=false,
        POWERPIPE_UPDATE_CHECK=false
      - '--min-instances'
      - '1'
      - '--max-instances'
      - '5'
      - '--timeout'
      - '3600s'
      - '--concurrency'
      - '10'
    waitFor: ['push-gcp']

  # Step 7: Deploy AWS Compliance Service to Cloud Run
  - name: gcr.io/cloud-builders/gcloud
    id: deploy-aws
    args:
      - run
      - deploy
      - steampipe-aws-compliance
      - '--image'
      - gcr.io/$PROJECT_ID/steampipe-aws-compliance
      - '--region'
      - us-central1
      - '--platform'
      - managed
      - '--allow-unauthenticated'
      - '--memory'
      - '8Gi'
      - '--cpu'
      - '4'
      - '--port'
      - '8080'
      - '--set-env-vars'
      - >-
        AUTH_TYPE=secret_manager,
        PROJECT_ID=$PROJECT_ID,
        STEAMPIPE_UPDATE_CHECK=false,
        POWERPIPE_UPDATE_CHECK=false
      - '--min-instances'
      - '1'
      - '--max-instances'
      - '5'
      - '--timeout'
      - '3600s'
      - '--concurrency'
      - '10'
    waitFor: ['push-aws']

  # Step 8: Deploy Azure Compliance Service to Cloud Run
  - name: gcr.io/cloud-builders/gcloud
    id: deploy-azure
    args:
      - run
      - deploy
      - steampipe-azure-compliance
      - '--image'
      - gcr.io/$PROJECT_ID/steampipe-azure-compliance
      - '--region'
      - us-central1
      - '--platform'
      - managed
      - '--allow-unauthenticated'
      - '--memory'
      - '8Gi'
      - '--cpu'
      - '4'
      - '--port'
      - '8080'
      - '--set-env-vars'
      - >-
        AUTH_TYPE=secret_manager,
        PROJECT_ID=$PROJECT_ID,
        STEAMPIPE_UPDATE_CHECK=false,
        POWERPIPE_UPDATE_CHECK=false
      - '--min-instances'
      - '1'
      - '--max-instances'
      - '5'
      - '--timeout'
      - '3600s'
      - '--concurrency'
      - '10'
    waitFor: ['push-azure']

  # Step 9: Summary and verification
  - name: gcr.io/cloud-builders/gcloud
    id: summary
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo "============================================"
        echo "✅ ALL 3 COMPLIANCE SERVICES DEPLOYED! ✅"
        echo "============================================"
        echo ""
        echo "Services deployed to Cloud Run:"
        echo "1. GCP Compliance: steampipe-gcp-compliance"
        echo "2. AWS Compliance: steampipe-aws-compliance"
        echo "3. Azure Compliance: steampipe-azure-compliance"
        echo ""
        echo "Region: us-central1"
        echo "Project: $PROJECT_ID"
        echo ""
        echo "Service URLs:"
        gcloud run services list --platform=managed --region=us-central1 --filter="metadata.name:steampipe-*-compliance" --format="table(metadata.name,status.url)"
        echo ""
        echo "🔗 View services in Console:"
        echo "https://console.cloud.google.com/run?project=$PROJECT_ID"
    waitFor: ['deploy-gcp', 'deploy-aws', 'deploy-azure']

# Build options
options:
  machineType: E2_HIGHCPU_8
  logging: CLOUD_LOGGING_ONLY
  env:
    - DOCKER_CLI_EXPERIMENTAL=enabled
    - DOCKER_BUILDKIT=1
  dynamicSubstitutions: true

# Timeout for the entire build and deployment
timeout: 2400s  # 40 minutes
