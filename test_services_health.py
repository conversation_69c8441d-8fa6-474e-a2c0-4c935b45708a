#!/usr/bin/env python3
"""
Test script to verify all three services are working correctly
"""

import requests
import json
import time
import sys

# Service URLs (update these with your actual deployed URLs)
SERVICES = {
    'gcp': 'https://steampipe-gcp-compliance-816268782019.us-central1.run.app',
    'aws': 'https://steampipe-aws-compliance-816268782019.us-central1.run.app', 
    'azure': 'https://steampipe-azure-compliance-816268782019.us-central1.run.app'
}

def test_health_endpoint(service_name, base_url):
    """Test the health endpoint of a service"""
    try:
        print(f"\n🔍 Testing {service_name.upper()} health endpoint...")
        response = requests.get(f"{base_url}/health", timeout=30)
        
        if response.status_code == 200:
            print(f"✅ {service_name.upper()} health check passed")
            return True
        else:
            print(f"❌ {service_name.upper()} health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ {service_name.upper()} health check error: {e}")
        return False

def test_benchmark_endpoint(service_name, base_url):
    """Test the benchmark endpoint of a service"""
    try:
        print(f"\n🔍 Testing {service_name.upper()} benchmark endpoint...")
        
        # Prepare test data based on service
        if service_name == 'gcp':
            test_data = {
                "benchmark": "cis_v300",
                "project_id": "auto",
                "secret_manager_name": "sa-gcp-local-secops"
            }
            endpoint = f"{base_url}/api/gcp/run-benchmark-async"
        elif service_name == 'aws':
            test_data = {
                "benchmark": "cis_v120", 
                "account_id": "aws",
                "secret_manager_name": "sa-aws-local-secops"
            }
            endpoint = f"{base_url}/api/aws/run-aws-benchmark-async"
        elif service_name == 'azure':
            test_data = {
                "benchmark": "cis_v200",
                "subscription_id": "azure", 
                "secret_manager_name": "sa-azure-local-secops"
            }
            endpoint = f"{base_url}/api/azure/run-azure-benchmark-async"
        
        response = requests.post(
            endpoint,
            headers={"Content-Type": "application/json"},
            json=test_data,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('status') == 'success':
                print(f"✅ {service_name.upper()} benchmark endpoint working - Job ID: {result.get('job_id')}")
                return True, result.get('job_id')
            else:
                print(f"❌ {service_name.upper()} benchmark failed: {result}")
                return False, None
        elif response.status_code == 503:
            print(f"⚠️  {service_name.upper()} service unavailable (503) - this is the issue we're fixing")
            return False, None
        else:
            print(f"❌ {service_name.upper()} benchmark failed: {response.status_code} - {response.text}")
            return False, None
            
    except Exception as e:
        print(f"❌ {service_name.upper()} benchmark error: {e}")
        return False, None

def main():
    """Main test function"""
    print("🚀 Testing Steampipe Compliance Services Health")
    print("=" * 60)
    
    all_healthy = True
    
    for service_name, base_url in SERVICES.items():
        print(f"\n📋 Testing {service_name.upper()} Service")
        print("-" * 40)
        
        # Test health endpoint
        health_ok = test_health_endpoint(service_name, base_url)
        
        # Test benchmark endpoint
        benchmark_ok, job_id = test_benchmark_endpoint(service_name, base_url)
        
        if not health_ok or not benchmark_ok:
            all_healthy = False
        
        print(f"\n📊 {service_name.upper()} Summary:")
        print(f"   Health: {'✅ PASS' if health_ok else '❌ FAIL'}")
        print(f"   Benchmark: {'✅ PASS' if benchmark_ok else '❌ FAIL'}")
        
        if job_id:
            print(f"   Job ID: {job_id}")
    
    print("\n" + "=" * 60)
    if all_healthy:
        print("🎉 ALL SERVICES ARE HEALTHY!")
        print("✅ The Steampipe connection fixes are working correctly.")
    else:
        print("❌ SOME SERVICES HAVE ISSUES")
        print("🔧 The fixes may need to be deployed or there are other issues.")
    
    print("\n💡 Next steps:")
    if all_healthy:
        print("   - Services are ready for production use")
        print("   - Monitor logs for any connection issues")
    else:
        print("   - Deploy the updated code using Cloud Deploy")
        print("   - Check service logs for detailed error information")
        print("   - Verify Secret Manager credentials are properly configured")
    
    return 0 if all_healthy else 1

if __name__ == "__main__":
    sys.exit(main())
